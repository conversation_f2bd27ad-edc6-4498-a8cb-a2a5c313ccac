using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using System.Threading;
using System.Runtime.InteropServices;
using System.Collections;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraGrid.Views.Layout.Events;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Layout;
using DevExpress.XtraGrid.Views.Layout.ViewInfo;
using System.Drawing.Imaging;
using PlatCommon.SysBase;
using DevExpress.XtraEditors;
using PlatCommon.Common;
using PlatPublic.Common;
using Tjhis.Nurinp.Station.Service;
using Tjhis.Nurinp.Station.SystemSetting;
using Tjhis.Nurinp.Station.ProcessMap;
using Tjhis.Nurinp.Station.Srv.ADTSrv;
using InPatManager = Tjhis.Nurinp.Station.Service.InPatManager;
using Tjhis.Nurinp.Station.Public;
using Tjhis.Doctor.Station.Global;
using NM_Service.NMService;
using System.Collections.ObjectModel;
using Model;
using DevExpress.XtraGrid.Views.Grid;
using Tjhis.Nurinp.Station.Bill;
using PlatCommon.Base02;
using PlatCommon.Base01;
using Tjhis.Nurinp.Station.CDSS;
using System.IO;
using PlatCommonForm;
using Tjhis_Mq_Service;
using System.Speech.Synthesis;
using System.Linq;

namespace Tjhis.Nurinp.Station.ADT
{
    public struct FLASHWINFO
    {
        public UInt32 cbSize;
        public IntPtr hwnd;
        public UInt32 dwFlags;
        public UInt32 uCount;
        public UInt32 dwTimeout;
    }
    public partial class frmBedSideCard : ParentForm
    {
        #region 变量
        //string pathEmptyImage = @"Images\System\empty.png";
        string pathEmptyImage = Application.StartupPath + "\\Images\\System\\empty.png";
        string pathFemaleImage = Application.StartupPath + "\\Images\\System\\female.png";
        string pathMaleImage = Application.StartupPath + "\\Images\\System\\male.png";
        string pathChildImage = Application.StartupPath + "\\Images\\System\\child.png";
        string pathGrilImage = Application.StartupPath + "\\Images\\System\\girl.png";
        //特级 1级 2级 3级 一般 护理等级图片
        //string pathSpecImage = @"Images\Menu\Small\75.png";
        //string pathFirstImage = @"Images\Menu\Small\76.png";
        //string pathSecondImage = @"Images\Menu\Small\77.png";
        //string pathThirdImage = @"Images\Menu\Small\78.png";
        //string pathCommonImage = @"Images\Menu\Small\79.png";
        ImageCollection smallIconmenu = null;
        ImageCollection largeIconmenu = null;

        Thread myThread;                                                        // 消息提收线程

        public bool IsSetting = false;                                   // 是否正在配置

        public MainFrm2 mainFrm2 = null; //打开的父窗体
        public Form frm =null;
        System.Drawing.Image myImagenull = null;
        List<int> orderList = null;

        DataSet myDs = null;
        System.Reflection.Assembly thisAssembly;

        // 医嘱灯相关
        string pathOrderStatus6 = Application.StartupPath + "\\Images\\System\\Circle_Red.png";
        string pathOrderStatus1 = Application.StartupPath + "\\Images\\System\\Circle_Green.png";

        System.Drawing.Image imgOrderStatus1 = null;
        System.Drawing.Image imgOrderStatus6 = null;

        public InPatManager inpBll = new InPatManager();            // 住院病人服务
        private DataSet dsOrderStatus0 = null;
        DataSet dscolor = null;
        GridView gridViewBak;
        LayoutView layoutViewBak;
        #endregion

        [DllImport("user32.dll")] //闪烁窗体
        public static extern bool FlashWindowEx(ref FLASHWINFO pwfi);

        // API声明 王代迪2017-02-16
        [System.Runtime.InteropServices.DllImport("kernel32.dll")]
        public static extern IntPtr GetModuleHandle(string name);

        public const UInt32 FLASHW_STOP = 0;        //停止闪动.系统将窗体恢复到初始状态.
        public const UInt32 FLASHW_CAPTION = 1;     //闪动窗体的标题.
        public const UInt32 FLASHW_TRAY = 2;        //闪动任务栏按钮
        public const UInt32 FLASHW_ALL = 3;         //闪动窗体标题和任务栏按钮
        public const UInt32 FLASHW_TIMER = 4;       //连续不停的闪动,直到此参数被设置为:FLASHW_STOP
        public const UInt32 FLASHW_TIMERNOFG = 12;  //连续不停的闪动,直到窗体用户被激活.通常用法将参数设置为: FLASHW_ALL | FLASHW_TIMERNOFG
        //add kangj 20161201
        private DateTime dtNow = DateTime.Now;      // 当前时间

        private bool blnOrderStatusAlert = true;    // 是否进行医嘱状态提醒
        private string blnOrderStatusInterval = "10000";//频率


        public delegate void GetRefreshCardHandler(object sender, EventArgs e);
        public static event GetRefreshCardHandler refreshCardEvent;
        System.Media.SoundPlayer player;
        bool isLoopPlay = false;//是否循环播放
        bool isLoopPlayed = false;//是否已经循环播放
        public static void OnFreshCard()
        {
            //刷新床头卡
            refreshCardEvent?.Invoke(null, new EventArgs());
        }

        public frmBedSideCard()
        {
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            //Tjhis_Mq_Factory.Init();
            //consumerSub();

            refreshCardEvent += refreshCard;
        }

        //2020 liul 

        public frmBedSideCard(MainFrm2 mainFrm)
        {
            this.mainFrm2 = mainFrm;
            InitializeComponent();
            // Tjhis_Mq_Factory.Init();
            //consumerSub();
            refreshCardEvent += refreshCard;
        }
        private void consumerSub()
        {
            try
            {
                List<string> list = new List<string>();
                list.Add("orderroutingkey");
                Tjhis_Mq_TriggerEvent infoEvent = new Tjhis_Mq_TriggerEvent();
                infoEvent.infoEvent += null;// orderAlert;
                Tjhis_Mq_Factory.Create_Consumer_PubSub(this.WardCode, list, "OderQueue", infoEvent);
            }
            catch (Exception ex)
            {
            }
        }

        delegate void GridControlCallBack(GridControl gridControl, DataSet ds);


        private void ShowGrid(GridControl gridControl, DataSet ds)
        {
            if (gridControl.InvokeRequired)
            {
                GridControlCallBack showmessageCallback = ShowGrid;
                gridControl.Invoke(showmessageCallback, new object[] { gridControl, ds });
            }
            else
            {
                getPatientByNursingClass();

                int currIndex = this.layoutView.FocusedRowHandle;

                gridControl.DataSource = null;
                //myDs.Tables[0].DefaultView.Sort = "BED_LABEL";
                gridControl.DataSource = myDs.Tables[0].DefaultView;

                this.layoutView.FocusedRowHandle = currIndex;
            }
        }


        #region 窗体事件
        /// <summary>
        /// 窗体加载
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmBedSideCard_Load(object sender, EventArgs e)
        {
            try
            {
                dscolor = new SrvBedSideCard().colorcode();
                DataTable dtPatientClass = new DataTable();
                dtPatientClass.Columns.Clear();
                dtPatientClass.Columns.Add("PATIENT_CLASS", typeof(string));

                //向表中添加数据
                dtPatientClass.Rows.Add(new object[] { "所有患者" });
                dtPatientClass.Rows.Add(new object[] { "在院患者" });
                dtPatientClass.Rows.Add(new object[] { "新入患者" });
                dtPatientClass.Rows.Add(new object[] { "男性患者" });
                dtPatientClass.Rows.Add(new object[] { "女性患者" });
                dtPatientClass.Rows.Add(new object[] { "特级护理" });
                dtPatientClass.Rows.Add(new object[] { "一级护理" });
                dtPatientClass.Rows.Add(new object[] { "二级护理" });
                dtPatientClass.Rows.Add(new object[] { "三级护理" });
                dtPatientClass.Rows.Add(new object[] { "新生儿" });
                dtPatientClass.Rows.Add(new object[] { "跌倒/坠床评分高风险患者" });
                //dtPatientClass.Rows.Add(new object[] { "精确查找" });
                repositoryItemLookUpEdit1.DataSource = dtPatientClass;
                repositoryItemLookUpEdit1.DisplayMember = "PATIENT_CLASS";
                repositoryItemLookUpEdit1.ValueMember = "PATIENT_CLASS";

                DataSet dsWard = new SrvBedSideCard().GetWardName();
                repositoryItemLookUpEdit2.DataSource = dsWard.Tables[0];
                repositoryItemLookUpEdit2.DisplayMember = "DEPT_NAME";
                repositoryItemLookUpEdit2.ValueMember = "DEPT_CODE";


                barEditItem2.EditValue = "所有患者";

                getPatientByNursingClass();

                timer1.Enabled = true;
                //2020 liul 大屏timer
                if (this.AppCode == "NURINPSCREEN")
                {
                    timerScroll.Interval = 5000;
                    timerScroll.Enabled = true;
                }

                thisAssembly = System.Reflection.Assembly.GetExecutingAssembly();
                //if (SystemParm.UdpClass != null)
                //{
                //    SystemParm.UdpClass.showMessageCallBack = new ShowMessageCallBack(RefreshBed);

                //    ThreadStart myThreaddelegate = new ThreadStart(SystemParm.UdpClass.ReceiveMsg);

                //    myThread = new Thread(myThreaddelegate);
                //    myThread.IsBackground = true;
                //    myThread.Start();
                //}

                //myImagenull = Image.FromFile(@"Images\System\nullorders.png");
                myImagenull = Image.FromFile(Application.StartupPath + "\\Images\\System\\nullorders.png");
                imgOrderStatus1 = Image.FromFile(pathOrderStatus1);
                imgOrderStatus6 = Image.FromFile(pathOrderStatus6);

                orderList = new List<int>();             
                this.blnOrderStatusAlert = SystemParm.GetParameterValue("ORDER_STATUS_ALERT",this.AppCode, this.WardCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode).Equals("1");
                string orderStatusInterval= SystemParm.GetParameterValue("ORDER_STATUS_INTERVAL", this.AppCode, this.WardCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                if (!string.IsNullOrEmpty(orderStatusInterval)) this.blnOrderStatusInterval = orderStatusInterval;
                this.timerOrderAlert.Interval = blnOrderStatusInterval.ToInt();
                // Updated by Wangdaidi 11:11 2021/12/3 D#607 护理病历床头卡自定义样式默认加载
                DataSet cardDesingn = new SrvBedSideCard().GetCardDesign("ADMIN","Default_Card");// 床头卡样式文件
                string fileContent = string.Empty;
                if (cardDesingn != null && cardDesingn.Tables[0].Rows.Count > 0)
                    fileContent = cardDesingn.Tables[0].Rows[0]["COLUMNXML"].ToString();
                string fileName = Application.StartupPath+ "\\BedSideCard.xml"; // 指定文件路径及名称
                File.WriteAllText(fileName, fileContent);
                layoutView.RestoreLayoutFromXml(fileName); //加载布局
               
                layoutView.CardMinSize = new Size(0, 0);
                layoutViewBak = layoutView;
                layoutViewBak.RestoreLayoutFromXml(fileName); //加载布局
                File.Delete(fileName);
                gridViewBak = new GridView();
                gridViewBak.OptionsView.ShowGroupPanel = false;
                gridViewBak.OptionsView.ColumnAutoWidth = false;
                gridViewBak.OptionsBehavior.Editable = false;
                gridViewBak.RowClick += new RowClickEventHandler(gridViewBak_RowClick);
                if (PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO == "ADMIN")//设置按钮控制，去掉管理员设置按钮自定义功能
                {
                    this.barBtnCustomization.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
                    repositoryItemRadioGroup1.Items[2].Enabled = false;
                }
                else
                {
                    this.barBtnCustomization.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
                    repositoryItemRadioGroup1.Items[2].Enabled = true;
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }


        private void layoutView_KeyUp(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.KeyCode == Keys.F2)
                {
                    TextInputFrm frm = new TextInputFrm();
                    frm.Text = "请输入床号 或 床标号 或 患者ID号 或 患者姓名 ";
                    if (frm.ShowDialog() != DialogResult.OK) return;

                    // 定位床号
                    FocusBed_ByBED_NO_OR_NAME(frm.InputedText);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
            }
        }


        private void layoutView1_CustomCardCaptionImage(object sender, LayoutViewCardCaptionImageEventArgs e)
        {
            try
            {
                object obj = layoutView.GetRow(e.RowHandle);
                if (obj == null) return;
                DataRow row = (obj as DataRowView).Row;

                if (row["NURSING_CLASS_NAME"] != DBNull.Value)
                {
                    switch (row["NURSING_CLASS_NAME"].ToString())
                    {
                        case "特级护理":
                            e.ImageIndex = 0;
                            break;
                        case "一级护理":
                            e.ImageIndex = 1;
                            break;
                        case "二级护理":
                            e.ImageIndex = 2;
                            break;
                        case "三级护理":
                            e.ImageIndex = 3;
                            break;
                        case "普通护理":
                            e.ImageIndex = 4;
                            break;
                        default:
                            e.ImageIndex = 5;
                            break;
                    }
                }
                else
                {
                    e.ImageIndex = 5;
                }
                RefreshData();
                //switch (row["ORDER_STATUS_1"].ToString())
                //{
                //    case "1":
                //        row["ORDERSSTATUS"] = Image.FromFile(pathOrderStatus1);
                //        break;
                //    case "6":
                //        row["ORDERSSTATUS"] = Image.FromFile(pathOrderStatus6);
                //        break;
                //    default:
                //        row["ORDERSSTATUS"] = myImagenull;
                //        break;
                //}
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        private void layoutView_CardClick(object sender, CardClickEventArgs e)
        {
            try
            {
                if (e.Button == MouseButtons.Right)
                {
                    string pat_id = layoutView.GetDataRow(e.RowHandle)["PATIENT_ID"] == null ? "" : layoutView.GetFocusedDataRow()["PATIENT_ID"].ToString();
                    if ("".Equals(pat_id))
                    {
                        if (e.Card.Text.Contains("的包床"))
                        {
                            // 隐藏右键菜单
                            ppMenu.HidePopup();
                            ParentForm frm1 = PlatCommon.Common.PublicFunction.GetFormInDll("Tjhis.Nurinp.Station.dll", "Tjhis.Nurinp.Station.ADT.frmContractBed");
                            ((ParentForm)frm1).PatientInfo = layoutView.GetFocusedDataRow();
                                frm1.WardCode = WardCode;
                                frm1.AppCode = AppCode;
                                frm1.Show();
                                return;
                        }
                        else
                        {
                            //return;
                            for (int i = 0; i < ppMenu.ItemLinks.Count; i++)
                            {
                                if (ppMenu.ItemLinks[i].Caption == "患者入科")
                                {
                                    ppMenu.ItemLinks[i].Visible = true;
                                }
                                else
                                {
                                    ppMenu.ItemLinks[i].Visible = false;
                                }
                            }
                            ppMenu.ShowPopup(PointToScreen(e.Location));
                        }
                    }
                    else
                    {
                        for (int i = 0; i < ppMenu.ItemLinks.Count; i++)
                        {
                            if (ppMenu.ItemLinks[i].Caption == "患者入科")
                            {
                                ppMenu.ItemLinks[i].Visible = false;
                            }
                            else
                            {
                                ppMenu.ItemLinks[i].Visible = true;
                            }
                        }
                        ppMenu.ShowPopup(PointToScreen(e.Location));
                    }
                    //对男患者关闭产程图显示 王代迪2017-06-21
                    if (layoutView.GetFocusedDataRow()["SEX"].ToString() == "男" || "".Equals(pat_id))
                    {
                        for (int i = 0; i < ppMenu.ItemLinks.Count; i++)
                        {
                            if (ppMenu.ItemLinks[i].Caption == "产程图")
                            {
                                ppMenu.ItemLinks[i].Visible = false;
                                break;
                            }
                        }
                        //ppMenu.ShowPopup(PointToScreen(e.Location));
                    }
                    else
                    {
                        for (int i = 0; i < ppMenu.ItemLinks.Count; i++)
                        {
                            if (ppMenu.ItemLinks[i].Caption == "产程图")
                            {
                                ppMenu.ItemLinks[i].Visible = true;
                                break;
                            }
                        }
                        //ppMenu.ShowPopup(PointToScreen(e.Location));
                    }

                    if (layoutView.GetFocusedDataRow()["CLINICAL_PATHWAY_STATUS"].ToString() != "径")
                    {
                        for (int i = 0; i < ppMenu.ItemLinks.Count; i++)
                        {
                            if (ppMenu.ItemLinks[i].Caption == "临床路径")
                            {
                                ppMenu.ItemLinks[i].Visible = false;
                                break;
                            }
                        }
                        //ppMenu.ShowPopup(PointToScreen(e.Location));
                    }
                    ppMenu.ShowPopup(PointToScreen(e.Location));
                }
                else if (e.Clicks >= 2)
                {
                    int ret = orderList.IndexOf(e.RowHandle);
                    if (ret < 0) return;
                    object obj = layoutView.GetRowCellValue(e.RowHandle, bc);
                    string baochuang = obj == null ? "" : obj.ToString();
                    if (!string.IsNullOrEmpty(baochuang))
                    {
                        return;
                    }
                    myDs.Tables[0].Columns["ORDERSSTATUS"].ReadOnly = false;
                    layoutView.SetRowCellValue(e.RowHandle, colORDERSSTATUS, myImagenull);
                    orderList.Remove(e.RowHandle);
                    //打开医嘱窗口
                    ParentForm frm1 = PlatCommon.Common.PublicFunction.GetFormInDll("NursingPlatform.exe", "NurInp.View.Orders.OrderAuditFrm");
                    if (frm1 != null)
                    {
                        frm1.StartPosition = FormStartPosition.CenterScreen;
                        frm1.PatientInfo = layoutView.GetDataRow(e.RowHandle);
                        frm1.ShowDialog();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }


        private void layoutView_MouseWheel(object sender, MouseEventArgs e)
        {
            try
            {
                this.layoutView.FocusedRowHandle = -1;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
            }
        }

        /// <summary>
        /// 定制卡片样式 Updated by Wangdaidi 14:31 2021/11/24 D#444 优化床头卡加载时间
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barBtnCustomization_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {

                #region 将自定义卡存储到本地 王代迪2017-02-16
                //202009 liul  大屏参数设置
                if (this.AppCode == "NURINPSCREEN")
                {
                    frmNrConfigerParameter frm = new frmNrConfigerParameter();
                    this.IsSetting = true;
                    this.timerScroll.Enabled = false;
                    this.mainFrm2.timer1.Enabled = false;
                    frm.ShowDialog(this);
                    this.mainFrm2.timer1.Enabled = true;
                    this.IsSetting = false;
                    this.timerScroll.Enabled = true;
                }
                else
                {
                    layoutView.ShowCustomizationForm();
                    string path = Application.StartupPath + "\\BedSideCard.xml";
                    layoutView.SaveLayoutToXml(path);
                    using (StreamReader sr = new StreamReader(path)) 
                    {
                        SrvBedSideCard srv = new SrvBedSideCard();
                        string fileContent = sr.ReadToEnd();
                        string itemClass = string.Empty;
                        string value = barEditItem3.EditValue.ToString();
                        if (SystemParm.LoginUser.EMP_NO=="ADMIN")
                        {
                            if (value == "0")
                                itemClass = "Default_Card";
                            else if (value == "1")
                                itemClass = "Simple_Card";
                            else
                            {
                                File.Delete(path);
                                return;
                            }
                        }
                        else
                        {
                            if (value == "2")
                                itemClass = "BedsideCard_" + SystemParm.LoginUser.EMP_NO;
                            else
                            {
                                File.Delete(path);
                                return;
                            }
                        }
                        DataSet schema = srv.GetCardDesign(SystemParm.LoginUser.EMP_NO, itemClass);
                        DataRow dr = null;
                        if (schema != null&&schema.Tables[0].Rows.Count>0)
                        {
                            dr = schema.Tables[0].Rows[0];
                            dr["COLUMNXML"] = fileContent;
                        }
                        else
                        {
                            schema = srv.GetCardDesingnSchema();
                            dr = schema.Tables[0].NewRow();
                            dr["COLUMNXML"] = fileContent;
                            dr["DOCTOR_CODE"] = SystemParm.LoginUser.EMP_NO;
                            dr["ITEM_CLASS"] = itemClass;
                            schema.Tables[0].Rows.Add(dr);
                        }
                        srv.SaveData(schema);
                        schema.AcceptChanges();
                    }
                    File.Delete(path);
                    // 加载布局
                    //if (System.IO.Directory.Exists(path) == false)
                    //{
                    //    System.IO.Directory.CreateDirectory(path);
                    //}
                    //// Updated by Wangdaidi 11:11 2021/12/3 D#607 护理病历床头卡自定义样式默认加载
                    ////layoutView.SaveLayoutToXml(path + "\\BedsideCard-" + SystemParm.LoginUser.USER_NAME + ".xml");

                }
                #endregion
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        private void barButtonItem3_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                layoutView.OptionsView.ViewMode = DevExpress.XtraGrid.Views.Layout.LayoutViewMode.SingleRecord;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        private void barButtonItem4_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                layoutView.OptionsView.ViewMode = DevExpress.XtraGrid.Views.Layout.LayoutViewMode.MultiColumn;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        private void barButtonItem5_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                layoutView.OptionsView.ViewMode = DevExpress.XtraGrid.Views.Layout.LayoutViewMode.MultiRow;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        private void barButtonItem6_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                layoutView.OptionsView.ViewMode = DevExpress.XtraGrid.Views.Layout.LayoutViewMode.Row;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        private void barButtonItem7_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                layoutView.OptionsView.ViewMode = DevExpress.XtraGrid.Views.Layout.LayoutViewMode.Carousel;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            try
            {
                timer1.Enabled = false;
                smallIconmenu = new ImageCollection();
                largeIconmenu = new ImageCollection();
                barManager1.Images = smallIconmenu;
                barManager1.LargeImages = largeIconmenu;
                this.LoadDataSet();
                //myDs.Tables[0].DefaultView.Sort = "BED_LABEL";
                gridControl1.DataSource = myDs.Tables[0].DefaultView;
                this.LoadRightMenu();

                timerOrderAlert.Enabled = this.blnOrderStatusAlert;

            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        private void refreshCard(object sender, EventArgs e)
        {
            if (this.AppCode == "NURINPSCREEN")
            {
                try
                {
                    this.Cursor = Cursors.WaitCursor;
                    this.LoadDataSet();
                    gridControl1.DataSource = myDs.Tables[0];
                }
                catch (Exception ex)
                {
                    this.Cursor = Cursors.Default;
                    MessageHelper.ShowInformation(ex.Message);
                    Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
                }
                finally
                {
                    this.Cursor = Cursors.Default;
                }
            }
            else
            {
                try
                {
                    this.Cursor = Cursors.WaitCursor;

                    RefreshData();
                }
                catch (Exception ex)
                {
                    this.Cursor = Cursors.Default;
                    MessageHelper.ShowInformation(ex.Message);
                    Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
                }
                finally
                {
                    this.Cursor = Cursors.Default;
                }
            }
        }
        /// <summary>
        /// 按钮[刷新]
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public void barButtonItem9_ItemClick(object sender, ItemClickEventArgs e)
        {
            refreshCard(null, null);
        }


        private void timerMsg_Tick(object sender, EventArgs e)
        {
            try
            {
                if (barButtonItem10.ImageIndex == 1)
                {
                    barButtonItem10.ImageIndex = 0;
                }
                else
                {
                    barButtonItem10.ImageIndex = 1;
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }


        /// <summary>
        /// 医嘱灯时钟
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        /// private void timerOrderAlert_Tick(object sender, EventArgs e)
        /// 
        //private void orderAlert(object sender, EventArgs e)
        private void timerOrderAlert_Tick(object sender, EventArgs e)
        {
            timerOrderAlert.Enabled = false;
            try
            {
                //// 获取医嘱提示病人
                //DataSet dsOrderStatus = inpBll.GetPatList_OrderAlert(this.WardCode);
                //if (!dsOrderStatus.Tables[0].Columns.Contains("ORDERSSUBMIT"))
                //{
                //    dsOrderStatus.Tables[0].Columns.Add("ORDERSSUBMIT", typeof(string)).ReadOnly = false;
                //}
                //if (!dsOrderStatus.Tables[0].Columns.Contains("ORDERSVERIFY"))
                //{
                //    dsOrderStatus.Tables[0].Columns.Add("ORDERSVERIFY", typeof(string)).ReadOnly = false;
                //}
                //if (dsOrderStatus0 == null)
                //{
                //    dsOrderStatus0 = dsOrderStatus.Copy();

                //    foreach (DataRow dr in dsOrderStatus0.Tables[0].Rows)
                //    {
                //        dr.SetModified();
                //    }
                //}
                //else
                //{
                //    Cs02DataSetHelper.MergeTable(dsOrderStatus0.Tables[0], dsOrderStatus.Tables[0]);
                //}

                //// 检查是否医嘱提示有变更
                //if (dsOrderStatus0.HasChanges() == false) return;

                //if (dsOrderStatus0.Tables[0].Select("SAVE_STATUS='1'").Length > 0 || dsOrderStatus0.Tables[0].Select("VERIFY_STATUS='1'").Length > 0)
                //{
                //    FlashWindow(this.Parent.Parent.Handle, FLASHW_TRAY | FLASHW_TIMER);

                //    DataTable dsOrderStatus1 = new DataTable();
                //    dsOrderStatus1 = dsOrderStatus0.Tables[0].Clone();
                //    DataRow[] dr = dsOrderStatus0.Tables[0].Select("SAVE_STATUS='1'");

                //    //for (int i = 0; i < dr.Length; i++)
                //    //{
                //    //    dsOrderStatus1.Rows.Add(dr[i].ItemArray);
                //    //}
                //    //if (dsOrderStatus1.Rows.Count > 0)
                //    //{
                //    //    bool isOpen = false;
                //    //    foreach (Form frm in Application.OpenForms)
                //    //    {
                //    //        if (frm is OrderMsg)
                //    //        {
                //    //            frm.StartPosition = FormStartPosition.CenterScreen;
                //    //            frm.Activate();
                //    //            isOpen = true;
                //    //            break;
                //    //        }
                //    //    }
                //    //    if (!isOpen)
                //    //    {
                //    //        OrderMsg frm = new OrderMsg();
                //    //        frm.dt = dsOrderStatus1;
                //    //        frm.StartPosition = FormStartPosition.CenterScreen;
                //    //        frm.Show();
                //    //    }
                //    //}
                //}
                //else
                //{
                //    FlashWindow(this.Parent.Parent.Handle, FLASHW_STOP);
                //}

                ////显示医嘱提示
                //RefreshBed_OrderStatus();

                // 获取医嘱提示病人
                DataSet dsOrderStatus0 = inpBll.GetPatList_OrderAlert(this.WardCode);
                if (dsOrderStatus0 == null) return;
                DataRow[] drFind = dsOrderStatus0.Tables[0].Select("SAVE_STATUS='1' or VERIFY_STATUS='1'");
                if (drFind != null && drFind.Length > 0)
                {
                    gridControlMsg.DataSource = drFind.CopyToDataTable();
                    dockPanelMsg.Visibility = DevExpress.XtraBars.Docking.DockVisibility.Visible;
                    FlashWindow(this.Parent.Parent.Handle, FLASHW_TRAY | FLASHW_TIMER);
                    #region Beep(500,1000);//增加声音提醒
                    string soundFile = Environment.CurrentDirectory + @"\Config\有新医嘱到了，请尽快处理.wav";//只能播放wav的音频文件
                    if (dsOrderStatus0.Tables[0].Select("YSSM='1'").Length > 0)
                    {
                        if (isLoopPlay)
                        {
                            //RefreshBed_OrderStatus();
                            return;
                        }
                        soundFile = Environment.CurrentDirectory + @"\Config\有紧急医嘱来了，请立即处理.wav";
                        isLoopPlay = true;
                    }
                    else
                    {
                        isLoopPlay = false;
                        if (isLoopPlayed) { player.Stop(); isLoopPlayed = false; }
                    }
                    if (!isLoopPlayed)
                    {
                        if (System.IO.File.Exists(soundFile))
                        {
                            try
                            {
                                player = new System.Media.SoundPlayer(soundFile);
                                if (isLoopPlay)
                                {
                                    player.PlayLooping();
                                    isLoopPlayed = true;
                                }
                                else
                                {
                                    player.Play();
                                    isLoopPlayed = false;
                                }

                            }
                            catch
                            {
                                SpeechSynthesizer speech1 = new SpeechSynthesizer();
                                speech1.Speak("有新医嘱到了,请尽快处理");
                            }
                        }
                        else
                        {
                            SpeechSynthesizer speech = new SpeechSynthesizer();
                            string str = "有新医嘱到了,请尽快处理";
                            speech.Speak(str);
                        }
                    }
                    #endregion
                }
                else
                {
                    if (isLoopPlayed)
                    {
                        player.Stop();
                        isLoopPlayed = false;
                        isLoopPlay = false;
                    }
                    gridControlMsg.DataSource = null;
                    dockPanelMsg.Visibility = DevExpress.XtraBars.Docking.DockVisibility.AutoHide;
                    FlashWindow(this.Parent.Parent.Handle, FLASHW_STOP);
                }
            }
            catch (Exception ex)
            {
                //MessageHelper.ShowError(ex.Message);
                //Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
            finally
            {
                timerOrderAlert.Enabled = true;
            }
        }
        private void gridViewMsg_RowStyle(object sender, RowStyleEventArgs e)
        {
            if (e.RowHandle < 0) return;
            DataRow dr = gridViewMsg.GetDataRow(e.RowHandle);
            if (dr.Table.Columns.Contains("YSSM"))
            {
                if (dr["YSSM"].ToString() == "1")
                {
                    e.Appearance.ForeColor = Color.Red;
                    e.Appearance.Options.UseForeColor = true;
                    gridViewMsg.Appearance.FocusedRow.ForeColor = Color.Red;
                    gridViewMsg.Appearance.FocusedRow.Options.UseForeColor = true;
                    gridViewMsg.Appearance.SelectedRow.ForeColor = Color.Red;
                    gridViewMsg.Appearance.SelectedRow.Options.UseForeColor = true;
                }
                else
                {
                    e.Appearance.Options.UseForeColor = false;
                    gridViewMsg.Appearance.FocusedRow.Options.UseForeColor = false;
                    gridViewMsg.Appearance.SelectedRow.Options.UseForeColor = false;
                }
            }
        }

        /// <summary>
        /// 窗口闪烁
        /// </summary>
        /// <param name="handle">窗口句柄</param>
        public static void FlashWindow(IntPtr handle, uint Timerss)
        {
            FLASHWINFO fInfo = new FLASHWINFO();

            fInfo.cbSize = Convert.ToUInt32(Marshal.SizeOf(fInfo));
            fInfo.hwnd = handle;
            fInfo.dwFlags = Timerss;//这里是闪动窗标题和任务栏按钮,直到用户激活窗体
            fInfo.uCount = UInt32.MaxValue;
            fInfo.dwTimeout = 0;

            FlashWindowEx(ref fInfo);
        }


        private void barButtonItem10_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                timerMsg.Enabled = false;
                barButtonItem10.ImageIndex = 1;
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        /// <summary>
        /// 右键菜单事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void RightMenuClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                if (e.Link != null)
                {
                    // 隐藏右键菜单
                    ppMenu.HidePopup();
                    Tjhis.Nurinp.Station.Service.Model.Comm.SEC_MENUS_DICT smd = e.Link.Data as Tjhis.Nurinp.Station.Service.Model.Comm.SEC_MENUS_DICT;
                    DataRow drPatient = null;
                    if (gridControl1.MainView.GetType().Name == "LayoutView")
                    {
                        drPatient = layoutView.GetFocusedDataRow();
                    }
                    else if (gridControl1.MainView.GetType().Name == "GridView")
                    {
                        drPatient = gridViewBak.GetFocusedDataRow();
                    }
                        //SEC_MENUS_DICT smd = e.Link.Data as SEC_MENUS_DICT;
                        this.WardCode = drPatient["WARD_CODE"].ToString();

                    if (smd != null && !string.IsNullOrEmpty(smd.OPEN_FORM.ToString()))
                    {
                        // 特例处理
                        // 划价Tjhis.Nurinp.Station.ADT.frmPachOrderBil
                        if (smd.OPEN_FORM.ToString().Equals("Tjhis.Nurinp.Station.Bill.frmPachOrderBill"))
                        {
                            //DataRow drPatient = layoutView.GetFocusedDataRow();
                            if (drPatient == null) return;
                            if (string.IsNullOrEmpty(drPatient["PATIENT_ID"].ToString())) return;

                            BatchBillHelper.CallBackBillPatch(drPatient["PATIENT_ID"].ToString());
                            return;
                        }

                        // 患者信息修改 遇到婴儿 变成 婴儿信息修改
                        if (smd.OPEN_FORM.ToString().Equals("Tjhis.Nurinp.Station.ADT.frmPatientInfoModify") || smd.OPEN_FORM.ToString().Equals("Tjhis.Nurinp.Station.ADT.frmNewBornAdmit"))
                        {
                            if (drPatient["IS_NEWBORN"].ToString().Equals("1"))
                            {
                                smd.OPEN_FORM = "Tjhis.Nurinp.Station.ADT.frmNewBornAdmit";
                            }
                            else
                            {
                                smd.OPEN_FORM = "Tjhis.Nurinp.Station.ADT.frmPatientInfoModify";
                            }
                        }
                        
                        //临床路径
                        if (smd.OPEN_FORM.ToString().Equals("Tjhis.ClinicalPath.Station.OpenSource.nurse.frmNurseExecute"))
                        {
                            //DataRow drPatient = layoutView.GetFocusedDataRow();
                            Tjhis.ClinicalPath.Station.OpenSource.nurse.frmNurseExecute frm2
                                = new Tjhis.ClinicalPath.Station.OpenSource.nurse.frmNurseExecute(drPatient["PATIENT_ID"].ToString(), int.Parse(drPatient["VISIT_ID"].ToString()));
                            frm2.StartPosition = FormStartPosition.CenterScreen;
                            frm2.ShowDialog();
                            return;
                        }

                        if(smd.OPEN_FORM.ToString().Equals("Tjhis.Nurinp.Station.ADT.frmBedExchange"))
                        {
                            frmBedExchange frm = new frmBedExchange();
                            //窗体变量赋值
                            SetFormParamValue(frm, drPatient);

                            //frm.PatientInfo= drPatient;
                            //frm.StartPosition = FormStartPosition.CenterScreen;
                            //frm.DeptCode = this.DeptCode;
                            //frm.WardCode = this.WardCode;
                            //frm.AppCode = this.AppCode;
                            //frm.DeptList = this.DeptList;
                            //frm.WardList = this.WardList;
                            //frm.WardName = this.WardName;
                            //frm.DeptName = this.DeptName;
                            //frm.Clinical_Pathway = this.Clinical_Pathway;
                            if (frm.ShowDialog() == DialogResult.OK)
                            {
                                RefreshData();
                                FocusBed_ByBED_NO_OR_NAME(frm.bedLabelTo);
                            }
                        }
                        // 正常处理
                        else
                        {
                            //DataRow row = layoutView.GetFocusedDataRow();
                            string[] prs = new string[] { drPatient["PATIENT_ID"].ToString(), drPatient["VISIT_ID"].ToString() };
                            ParentForm frm1;
                            if (smd.OPEN_FORM.Equals("Tjhis.Nurinp.Station.NurRec.frmNursingDoc"))
                            {
                                frm1 = (ParentForm)(CommonMethod.GetFormInDll(smd.OPEN_FILE_NAME.ToString(), smd.OPEN_FORM.ToString()));

                            }
                            else
                            {
                                frm1 = (ParentForm)(CommonMethod.GetFormInDll(smd.OPEN_FILE_NAME.ToString(), smd.OPEN_FORM.ToString(), prs));

                            }
                            if (frm1 != null)
                            {
                                //if (smd.OPEN_FORM.ToString().Equals("Tjhis.Nurinp.Station.ADT.frmNewBornAdmit")) frm1.Tag = "mod"; // 婴儿信息修改

                                //// 设置图标
                                //string iconFile = string.Empty;
                                //if (!string.IsNullOrEmpty(smd.SMALL_ICON))
                                //{
                                //    iconFile = @"" + smd.SMALL_ICON;
                                //}

                                //if (!string.IsNullOrEmpty(smd.LARGE_ICON))
                                //{
                                //    iconFile = @"" + smd.LARGE_ICON;
                                //}

                                // 设置参数
                                //frm1.StartPosition = FormStartPosition.CenterScreen;
                                // 要主窗体中打开该窗体
                                //if (smd["OPEN_FORM"].ToString().Contains("Tjhis.Nurinp.Station.ADT.frmOrderAudit") || smd["OPEN_FORM"].ToString().Contains("Tjhis.Nurinp.Station.ADT.frmNursingDoc"))
                                //{
                                //    ((Tjhis.Nurinp.Station.ADT.ParentForm)frm1).PatientInfo = layoutView.GetFocusedDataRow();
                                //    His00MainTabView.OpenFrm(ref frm1);
                                //    //Tjhis.Nurinp.Station.ADT.MainFrm.Instance.openForm(frm1, iconFile);
                                //}
                                //else
                                //{
                                //    ((Tjhis.Nurinp.Station.ADT.ParentForm)frm1).PatientInfo = layoutView.GetFocusedDataRow();
                                //    frm1.ShowDialog();
                                //    frm1.Dispose();
                                //}
                                //((ParentForm)frm1).PatientInfo = drPatient;
                                ////DataRow row = layoutView.GetFocusedDataRow();
                                ////string[] prs = new string[] { row["PATIENT_ID"].ToString(), row["VISIT_ID"].ToString() };
                                ////His00MainTabView.OpenFrm(ref frm1, prs);
                                //DataTable row = new DataTable();
                                //row = ((Tjhis.Nurinp.Station.ADT.ParentForm)frm1).PatientInfo.Table.Clone();
                                //row.Rows.Add(((Tjhis.Nurinp.Station.ADT.ParentForm)frm1).PatientInfo.ItemArray);
                                //string[] prs = new string[] { row.Rows[0]["PATIENT_ID"].ToString(), row.Rows[0]["VISIT_ID"].ToString() };
                                //His00MainTabView.OpenFrm(ref frm1,prs);                               
                                //frm1.Show();
                                //窗体变量赋值
                                SetFormParamValue(frm1, drPatient);
                                //PlatCommon.SysBase.SystemParm.openNurswsFormHandle(frm1, string.Empty);
                                 
                                  Control parentControl = this.Parent.Parent;
                                if (parentControl != null && parentControl.GetType() == typeof(FrmNewMain))
                                {
                                    FrmNewMain main = (FrmNewMain)parentControl;
                                    main.openForm(frm1, string.Empty);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }


        private void SetFormParamValue(ParentForm newForm, DataRow drPatient)
        {
            newForm.PatientInfo = drPatient;
            newForm.StartPosition = FormStartPosition.CenterScreen;
            newForm.DeptCode = this.DeptCode;
            newForm.WardCode = this.WardCode;
            newForm.AppCode = this.AppCode;
            newForm.DeptList = this.DeptList;
            newForm.WardList = this.WardList;
            newForm.WardName = this.WardName;
            newForm.DeptName = this.DeptName;
            newForm.Clinical_Pathway = this.Clinical_Pathway;
            newForm.NewBornOut = this.NewBornOut;
            newForm.ClinicClass = this.ClinicClass;
            newForm.AppName = this.AppName;
        }
        #endregion


        #region 共通函数
        /// <summary>
        /// 取得行号
        /// </summary>
        /// <param name="bed_no"></param>
        /// <param name="ward_code"></param>
        /// <returns></returns>
        public int GetRowHandleByKeys(string bed_no, string ward_code)
        {
            int result = -1;
            DataRow dRow;
            for (int j = 0; j < layoutView.DataRowCount; j++)
            {
                dRow = layoutView.GetDataRow(j);
                if (bed_no.Equals(dRow["BED_NO"].ToString()) && ward_code.Equals(dRow["WARD_CODE"].ToString()))
                {
                    result = j;
                    break;
                }
            }
            return result;
        }


        /// <summary>
        /// 取得行号
        /// </summary>
        /// <param name="bed_no"></param>
        /// <param name="ward_code"></param>
        /// <returns></returns>
        public void FocusBed_ByBED_NO_OR_NAME(string bed_no)
        {
            int result = -1;
            DataRow dRow;
            for (int j = 0; j < layoutView.DataRowCount; j++)
            {
                dRow = layoutView.GetDataRow(j);
                if (bed_no.Equals(dRow["BED_NO"].ToString()) || dRow["NAME"].ToString().Contains(bed_no) || dRow["BED_LABEL"].ToString().Contains(bed_no) || dRow["PATIENT_ID"].ToString().Contains(bed_no))
                {
                    result = j;
                    break;
                }
            }

            if (result == -1) return;

            layoutView.FocusedRowHandle = result;
        }


        public Bitmap ResizeImage(Image image, int height, int maxWidth)
        {
            int width = Math.Min(image.Width * height / image.Height, maxWidth);
            int heightImage = image.Height * width / image.Width;
            Rectangle destRect = new Rectangle(0, (height - heightImage) / 2, width, heightImage);
            Bitmap destImage = new Bitmap(width, height);

            using (Graphics graphics = Graphics.FromImage(destImage))
            {
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.DrawImage(image, destRect, 0, 0, image.Width, image.Height, GraphicsUnit.Pixel);
            }

            return destImage;
        }


        /// <summary>
        /// 加载床头卡
        /// </summary>
        protected void LoadDataSet()
        {
            InPatManager inpBllComm = new InPatManager();

            SrvBedSideCard client = new SrvBedSideCard();

            dtNow = new NM_Service.NMService.ServerPublicClient().GetSysDate();

            myDs = client.GetBedSideCardBySql(this.WardCode, SystemParm.HisUnitCode);
            dsOrderStatus0 = inpBll.GetPatList_OrderAlert(this.WardCode);
            if (!dsOrderStatus0.Tables[0].Columns.Contains("ORDERSSUBMIT"))
            {
                dsOrderStatus0.Tables[0].Columns.Add("ORDERSSUBMIT", typeof(string)).ReadOnly = false;
            }
            if (!dsOrderStatus0.Tables[0].Columns.Contains("ORDERSVERIFY"))
            {
                dsOrderStatus0.Tables[0].Columns.Add("ORDERSVERIFY", typeof(string)).ReadOnly = false;
            }
            if (myDs != null && myDs.Tables.Count > 0 && myDs.Tables[0].Columns.Contains("DEPT_CODE"))
            {
                this.DeptCode = myDs.Tables[0].Rows[0]["DEPT_CODE"].ToString();
            }

            // 获取参数 
            // SystemParm parmBll = new SystemParm();

            // balanceContainOverDraw  余额中含透支额(1 不含, 0 含)
            string balanceContainOverDraw = SystemParm.GetParameterValue("PAYPERMENT_BALANCE", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (string.IsNullOrEmpty(balanceContainOverDraw))
            {
                balanceContainOverDraw = "1";
            }

            // gs_bill_mother_and_born 母亲与婴儿的费用是否合并 (1: 合并 0: 不合并)
            string gs_bill_mother_and_born = SystemParm.GetParameterValue("BILL_MOTHER_AND_BORN", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (string.IsNullOrEmpty(gs_bill_mother_and_born))
            {
                gs_bill_mother_and_born = "1";
            }
            // 透支方式(OVERDRAFT_WAY)：平台级；取值：1 - 各个透支排斥；2 - 各个透支联合，即相加；
            string way = SystemParm.GetParameterValue("OVERDRAFT_WAY", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
            if (string.IsNullOrEmpty(way))
            {
                gs_bill_mother_and_born = "2";
            }
            decimal ldc_approve = 0;
            string overDraftPatientId = null;
            string overDraftVisitId = null;

            if (myDs != null)
            {
                myDs.Tables[0].Columns.Add("ORDERSSTATUS", typeof(Image)).ReadOnly = false;
                myDs.Tables[0].Columns.Add("ORDERSSTATUS_VERIFY", typeof(Image)).ReadOnly = false;

                byte[] empty = ImageHelper.ImageToBytes(pathEmptyImage, System.Drawing.Imaging.ImageFormat.Png);//ImageHelper.ImageToBytes(new BitmapImage(new Uri(@"..\..\Images\empty.png", UriKind.Relative)));
                byte[] male = ImageHelper.ImageToBytes(pathMaleImage, System.Drawing.Imaging.ImageFormat.Png);//ImageHelper.ImageToBytes(new BitmapImage(new Uri(@"..\..\Images\male.png", UriKind.Relative)));
                byte[] female = ImageHelper.ImageToBytes(pathFemaleImage, System.Drawing.Imaging.ImageFormat.Png);//ImageHelper.ImageToBytes(new BitmapImage(new Uri(@"..\..\Images\female.png", UriKind.Relative)));
                byte[] child = ImageHelper.ImageToBytes(pathChildImage, System.Drawing.Imaging.ImageFormat.Png);//ImageHelper.ImageToBytes(new BitmapImage(new Uri(@"..\..\Images\female.png", UriKind.Relative)));
                byte[] girl = ImageHelper.ImageToBytes(pathGrilImage, System.Drawing.Imaging.ImageFormat.Png);
                foreach (DataRow dr in myDs.Tables[0].Rows)
                {   //2021-1-5 liul 程序打开床头卡加载速度慢
                    //// 获取病人的可用金额
                    //if (dr["PATIENT_ID"] != DBNull.Value)
                    //{
                    //    overDraftPatientId = dr["PATIENT_ID"].ToString();
                    //    overDraftVisitId = dr["VISIT_ID"].ToString();
                    //    //new billPublicSrv().GetOverDraft(this.AppCode, overDraftPatientId, overDraftVisitId, ref ldc_approve, way);

                    //    dr["PREPAYMENT_BAL"] = inpBllComm.GetAvailableMoney(overDraftPatientId, overDraftVisitId, balanceContainOverDraw, way, gs_bill_mother_and_born);
                    //}

                    if (dr["IMAGE"] == DBNull.Value)
                    {
                        if (dr["IS_NEWBORN"].ToString().Equals("1"))
                        {
                            // dr["IMAGE"] = child;
                            //liul 增加婴儿女孩床头卡
                            if (dr["SEX"] == DBNull.Value)
                            {
                                dr["IMAGE"] = empty;
                            }
                            else if ("男".Equals(dr["SEX"]))
                            {
                                dr["IMAGE"] = child;
                            }
                            else if ("女".Equals(dr["SEX"]))
                            {
                                dr["IMAGE"] = girl;
                            }
                        }
                        else
                        {
                            if (dr["SEX"] == DBNull.Value)
                            {
                                dr["IMAGE"] = empty;
                            }
                            else if ("男".Equals(dr["SEX"]))
                            {
                                dr["IMAGE"] = male;
                            }
                            else if ("女".Equals(dr["SEX"]))
                            {
                                dr["IMAGE"] = female;
                            }
                        }
                    }

                    dr["ORDERSSTATUS"] = myImagenull;
                    dr["ORDERSSTATUS_VERIFY"] = myImagenull;

                    // 设置医嘱提示灯
                    if (dr["BED_NO"].ToString().Length == 0) continue;

                    string filter = "BED_NO = " + dr["BED_NO"].ToString();
                    DataRow[] drFind = dsOrderStatus0.Tables[0].Select(filter);
                    if (drFind.Length == 0) continue;

                    DataRow drStatus = drFind[0];

                    // 医生提交提醒
                    if (this.blnOrderStatusAlert)
                    {
                        if (drStatus["SAVE_STATUS"].ToString().Equals("1"))
                        {
                            dr["ORDERSSTATUS"] = imgOrderStatus1;
                        }

                        // 护士校对提醒
                        if (drStatus["VERIFY_STATUS"].ToString().Equals("1"))
                        {
                            dr["ORDERSSTATUS_VERIFY"] = imgOrderStatus6;
                        }
                    }

                    //临床路径
                    //if (dr.Table.Columns.Contains("CLINICAL_PATHWAY_STATUS") && (dr["CLINICAL_PATHWAY_STATUS"].ToString().Equals("1") || dr["CLINICAL_PATHWAY_STATUS"].ToString().Equals("0")))
                    //{
                    //    dr["CLINICAL_PATHWAY_STATUS"] = "径";
                    //}

                    // 非计划拔管
                    if (dr.Table.Columns.Contains("DECANNULATIONFLAG") && (dr["DECANNULATIONFLAG"].ToString().Equals("1")))
                    {
                        dr["DECANNULATIONFLAG"] = "拔管";
                    }
                }
            }
        }


        /// <summary>
        /// 添加子菜单
        /// </summary>
        /// <param name="smd"></param>
        /// <returns></returns>
        private bool AddSubMenu(Tjhis.Nurinp.Station.Service.Model.Comm.SEC_MENUS_DICT smd)
        {
            foreach (BarItemLink bss in ppMenu.ItemLinks)
            {
                if (bss != null)
                {
                    if (smd.SUPPER_MENU.Equals(bss.Item.Name))
                    {
                        BarButtonItem bbi = new BarButtonItem(barManager1, smd.MENU_TEXT);
                        bbi.Tag = smd;
                        bbi.Category.Name = smd.SUPPER_MENU;
                        //bbi.Caption = smd.MENU_NAME;
                        //bil.Data = smd;
                        BarSubItem ddd = bss.Item as BarSubItem;
                        if (!string.IsNullOrEmpty(smd.SMALL_ICON))
                        {
                            smallIconmenu.AddImage(Image.FromFile(@"" + smd.SMALL_ICON));
                            bbi.ImageIndex = smallIconmenu.Images.Count - 1;
                        }

                        if (!string.IsNullOrEmpty(smd.LARGE_ICON))
                        {
                            largeIconmenu.AddImage(Image.FromFile(@"" + smd.LARGE_ICON));
                            bbi.LargeImageIndex = largeIconmenu.Images.Count - 1;
                        }

                        bbi.ItemClick += new ItemClickEventHandler(RightMenuClick);
                        ddd.AddItem(bbi).Data = smd;

                        return true;
                    }
                }
            }
            return false;
        }


        /// <summary>
        /// 加载右键菜单
        /// </summary>
        //protected void LoadRightMenu()
        //{
        //    barManager1.MainMenu = bar1;
        //    smallIconmenu.Clear();
        //    largeIconmenu.Clear();
        //    string myCaption = "";
        //    List<BarItem> lisss = new List<BarItem>();
        //    bool isSperateLine = false;
        //    //获取右键菜单项
        //    SrvBedSideCard client = new SrvBedSideCard();

        //    //ObservableCollection<SEC_MENUS_DICT> mlist = client.GetObservableCollection_SEC_MENUS_DICT(0, 0, " menu_group='床头卡右键菜单'", " SERIAL_NO ");
        //    //ObservableCollection<SEC_MENUS_DICT> mlist = client.GetBedSideRightMenus(false, this.AppCode, (false ? SystemParm.LoginUser.USER_NAME : SystemParm.LoginUser.USER_NAME), "床头卡右键菜单", " ITEM_NO");
        //    //DataTable mlist = client.GetBedSideRightMenus(this.AppCode, Tjhis.Doctor.Station.Global.CurrentUser.UserName, "CONTROL");
        //    DataTable mlist = client.GetBedSideRightMenus(this.AppCode, PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, "CONTROL");
        //    if (mlist == null) return;
        //    //for (int i = mlist.Count - 1; i >= 0;i-- )
        //    for (int i = 0; i < mlist.Rows.Count; i++)
        //    {
        //        myCaption = mlist.Rows[i]["DESCRIPTION"].ToString(); ;
        //        BarSubItem bsi = null;
        //        bsi = new BarSubItem(barManager1, mlist.Rows[i]["DESCRIPTION"].ToString());
        //        bsi.Name = mlist.Rows[i]["DESCRIPTION"].ToString();
        //        bsi.Tag = mlist.Rows[i];

        //        //switch (myCaption)
        //        //{
        //        //    case "医嘱执行":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/02-医嘱执行.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/02-医嘱执行.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "患者体温":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/00-患者体温.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/00-患者体温.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "护理病历":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/00-护理病历.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/00-护理病历.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "患者输液":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/07-输液.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/07-输液.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "患者转出":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/00-患者转出.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/00-患者转出.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "患者出观":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/00-患者出院.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/00-患者出院.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "取消入观":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/00-取消入科.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/00-取消入科.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "换床处理":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/00-换床处理.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/00-换床处理.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "患者信息修改":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/00-患者信息修改.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/00-患者信息修改.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "床头卡/腕带打印":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/00-床头卡腕带打印.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/00-床头卡腕带打印.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    case "患者信息查询":
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/00-患者信息查询.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/00-患者信息查询.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //    default:
        //        //        smallIconmenu.AddImage(Image.FromFile(@"Images/Menu/Big/02-护理计划.png"));
        //        //        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
        //        //        largeIconmenu.AddImage(Image.FromFile(@"Images/Menu/Small/02-护理计划.png"));
        //        //        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
        //        //        break;
        //        //}

        //        if (!String.IsNullOrEmpty(mlist.Rows[i]["OPEN_FORM"].ToString()))
        //        {
        //            bsi.ItemClick += new ItemClickEventHandler(RightMenuClick);
        //        }
        //        BarItemLink bil = ppMenu.ItemLinks.Add(bsi);
        //        bil.Data = mlist.Rows[i];
        //        if (isSperateLine)
        //        {
        //            bil.BeginGroup = true;
        //            isSperateLine = false;
        //        }


        //        //去掉没有子菜单的菜单箭头
        //        foreach (BarItemLink bss in ppMenu.ItemLinks)
        //        {

        //            if (bss != null)
        //            {
        //                BarSubItem bsi1 = bss.Item as BarSubItem;
        //                bsi1.AllowDrawArrow = DefaultBoolean.False;
        //            }
        //        }
        //    }
        //    //增加患者入科
        //    if (this.AppCode == "NURINP"|| this.AppCode == "NURSWS")
        //    {
        //        BarSubItem bsi3 = new BarSubItem(barManager1, "患者入科");
        //        bsi3.Name = "患者入科";
        //        DataTable dt = mlist.Clone();
        //        DataRow drEdit = dt.NewRow();
        //        drEdit["OPEN_FORM"] = "Tjhis.Nurinp.Station.ADT.frmPatientIn";
        //        drEdit["OPEN_FILE"] = "TJHIS_NurInp_ADT.dll";
        //        dt.Rows.Add(drEdit);
        //        bsi3.Tag = dt.Rows[0];
        //        bsi3.AllowDrawArrow = DefaultBoolean.False;
        //        bsi3.ItemClick += new ItemClickEventHandler(RightMenuClick);
        //        BarItemLink bil2 = ppMenu.ItemLinks.Add(bsi3);
        //        bil2.Data = dt.Rows[0];
        //        if (isSperateLine)
        //        {
        //            bil2.BeginGroup = true;
        //            isSperateLine = false;
        //        }
        //    }
        //}

        /// <summary>
        /// 加载右键菜单
        /// </summary>
        protected void LoadRightMenu()
        {
            barManager1.MainMenu = bar1;
            smallIconmenu.Clear();
            largeIconmenu.Clear();
            string myCaption = "";
            List<BarItem> lisss = new List<BarItem>();
            bool isSperateLine = false;

            // 添加顶部菜单 - 日常工作
            AddDailyWorkMenu();

            //获取右键菜单项
            ObservableCollection<Tjhis.Nurinp.Station.Service.Model.Comm.SEC_MENUS_DICT> mlist = new SrvBedSideCard().GetBedSideRightMenus_EMR(this.AppCode, PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, "床头卡右键菜单", " SERIAL_NO ", PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (mlist == null) return;
            for (int i = mlist.Count - 1; i >= 0; i--)
            {
                myCaption = mlist[i].MENU_TEXT;
                BarSubItem bsi = null;
                if ("parent".Equals(mlist[i].SUPPER_MENU.ToLower()))
                {
                    if ("-".Equals(mlist[i].MENU_TEXT))
                    {
                        //分割线，不加入菜单
                        isSperateLine = true;
                        continue;
                    }
                    bsi = new BarSubItem(barManager1, mlist[i].MENU_TEXT);
                    bsi.Name = mlist[i].MENU_NAME;
                    bsi.Tag = mlist[i];

                    if (!string.IsNullOrEmpty(mlist[i].SMALL_ICON))
                    {
                        smallIconmenu.AddImage(Image.FromFile(@"" + mlist[i].SMALL_ICON));
                        bsi.ImageIndex = smallIconmenu.Images.Count - 1;
                    }

                    if (!string.IsNullOrEmpty(mlist[i].LARGE_ICON))
                    {
                        largeIconmenu.AddImage(Image.FromFile(@"" + mlist[i].LARGE_ICON));
                        bsi.LargeImageIndex = largeIconmenu.Images.Count - 1;
                    }

                    //bsi.Caption = mlist[i].MENU_TEXT;
                    if (!String.IsNullOrEmpty(mlist[i].OPEN_FORM))
                    {
                        bsi.ItemClick += new ItemClickEventHandler(RightMenuClick);
                    }
                    BarItemLink bil = ppMenu.ItemLinks.Add(bsi);
                    bil.Data = mlist[i];
                    if (isSperateLine)
                    {
                        bil.BeginGroup = true;
                        isSperateLine = false;
                    }
                    //ppMenu.ItemLinks.Add(bsi).Data = mlist[i];
                    //bil = ppMenu.ItemLinks.Add(bsi);
                    mlist.RemoveAt(i);
                    //bil.Data = mlist[i];    
                }
            }
            foreach (Tjhis.Nurinp.Station.Service.Model.Comm.SEC_MENUS_DICT smd in mlist)
            {
                AddSubMenu(smd);

            }
            //去掉没有子菜单的菜单箭头
            foreach (BarItemLink bss in ppMenu.ItemLinks)
            {

                if (bss != null)
                {
                    Tjhis.Nurinp.Station.Service.Model.Comm.SEC_MENUS_DICT smd = bss.Data as Tjhis.Nurinp.Station.Service.Model.Comm.SEC_MENUS_DICT;
                    if ("parent".Equals(smd.SUPPER_MENU))
                    {
                        BarSubItem bsi = bss.Item as BarSubItem;
                        if (bsi.ItemLinks != null && bsi.ItemLinks.Count > 0)
                        {
                            bsi.AllowDrawArrow = DefaultBoolean.True;
                        }
                        else
                        {
                            bsi.AllowDrawArrow = DefaultBoolean.False;
                        }
                    }
                }
            }
            int sess = ppMenu.ItemLinks.Count;
        }


        public void getPatientByNursingClass()
        {
            DataSet ds = new SrvBedSideCard().nursingCount(this.WardCode);


            barStaticItem3.Caption = "特级护理:" + ds.Tables[0].Rows[0]["criticalCare"].ToString() + " 人";
            barStaticItem4.Caption = "一级护理:" + ds.Tables[0].Rows[0]["firstClassNursing"].ToString() + " 人";
            barStaticItem5.Caption = "二级护理:" + ds.Tables[0].Rows[0]["secondaryCare"].ToString() + " 人";
            barStaticItem6.Caption = "三级护理:" + ds.Tables[0].Rows[0]["tertiaryCare"].ToString() + " 人";
            barStaticItem12.Caption = "男:" + ds.Tables[0].Rows[0]["men"].ToString() + " 人";
            barStaticItem13.Caption = "女:" + ds.Tables[0].Rows[0]["girl"].ToString() + " 人";
            barStaticItem11.Caption = "在院患者:" + (ds.Tables[0].Rows[0]["men"].ToInt() + ds.Tables[0].Rows[0]["girl"].ToInt()) + " 人";
            barStaticItem8.Caption = "床位:" + ds.Tables[0].Rows[0]["bed_count"].ToString() + " 张";
            barStaticItem14.Caption = "新入:" + ds.Tables[0].Rows[0]["news"].ToString() + " 人 ";

            barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
            barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
            barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
            barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
            barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
            barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
            barStaticItem11.ItemAppearance.Normal.BackColor = Color.Lavender;
            barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;

            //设置护理级别标识颜色
            GetNursColor();
        }

        /// <summary>
        /// 刷新界面
        /// </summary>
        private void RefreshData()
        {
            getPatientByNursingClass();

            this.LoadDataSet();
            
            gridControl1.DataSource = myDs.Tables[0].DefaultView;
            LayoutViewInfo viewInfo = layoutView.GetViewInfo() as LayoutViewInfo;
            int visibleCards = viewInfo.VisibleCards.Count;
            if (visibleCards == 0)
            {
                layoutView.ActiveFilterString = "";
            }
        }


        /// <summary>
        /// 刷新床位
        /// </summary>
        /// <param name="pn"></param>
        public void RefreshBed(CustomMessage pn)
        {
            // 刷新床位
            if (pn.MsgType.Equals("1") && pn.MsgWardCode.Equals(this.WardCode))
            {
                this.LoadDataSet();
                ShowGrid(gridControl1, myDs);
            }

            // 新医嘱标识设置
            if (pn.MsgType.Equals("2") && pn.MsgWardCode.Equals(this.WardCode))
            {
                //timerMsg.Enabled = true;

                //// 更新床位标志
                //int hand = this.GetRowHandleByKeys(pn.BedNo, pn.MsgWardCode);

                //if (hand >= 0)
                //{
                //    int ret = orderList.IndexOf(hand);
                //    if (ret >= 0) return;

                //    // 设置新遗嘱标志
                //    orderList.Add(hand);

                //    myDs.Tables[0].Columns["ORDERSSTATUS"].ReadOnly = false;
                //    layoutView.SetRowCellValue(hand, colORDERSSTATUS, myImage);
                //}
            }
        }


        /// <summary>
        /// 刷新床位
        /// </summary>
        /// <param name="pn"></param>
        private void RefreshBed_OrderStatus()
        {
            // 预处理
            if (Cs02DataSetHelper.HasRecord(myDs) == false) return;
            myDs.Tables[0].Columns["ORDERSSTATUS"].ReadOnly = false;
            myDs.Tables[0].Columns["ORDERSSTATUS_VERIFY"].ReadOnly = false;
            // 查询医嘱状态变更了的病人
            DataRow[] drFind = dsOrderStatus0.Tables[0].Select("", "", DataViewRowState.ModifiedCurrent | DataViewRowState.ModifiedOriginal);

            for (int i = 0; i < drFind.Length; i++)
            {
                DataRow dr = drFind[i];

                // 更新床位标志
                int hand = this.GetRowHandleByKeys(dr["BED_NO"].ToString(), this.WardCode);

                if (hand >= 0)
                {
                    // 医生提交提醒
                    if (dr["SAVE_STATUS"].ToString().Equals("1"))
                    {
                        layoutView.SetRowCellValue(hand, colORDERSSTATUS, imgOrderStatus1);
                        dr["ORDERSSUBMIT"] = "新医嘱";
                    }
                    else
                    {
                        layoutView.SetRowCellValue(hand, colORDERSSTATUS, myImagenull);
                        dr["ORDERSSUBMIT"] = "";
                    }

                    // 护士校对提醒
                    if (dr["VERIFY_STATUS"].ToString().Equals("1"))
                    {
                        layoutView.SetRowCellValue(hand, colORDERSTATUS_VERIFY, imgOrderStatus6);
                        dr["ORDERSVERIFY"] = "待审核";
                    }
                    else
                    {
                        layoutView.SetRowCellValue(hand, colORDERSTATUS_VERIFY, myImagenull);
                        dr["ORDERSVERIFY"] = "";
                    }
                }
            }

            layoutView.Refresh();

            dsOrderStatus0.AcceptChanges();
            DataRow[] drsOrdersStatus = dsOrderStatus0.Tables[0].Select("ORDERSSUBMIT = '新医嘱' OR ORDERSVERIFY = '待审核'");

            if (drsOrdersStatus.Length > 0)
            {
                DataTable dtMsg = drsOrdersStatus.CopyToDataTable();
                gridControlMsg.DataSource = dtMsg;
                dockPanelMsg.Visibility = DevExpress.XtraBars.Docking.DockVisibility.Visible;
            }
            else
            {
                gridControlMsg.DataSource = null;
                dockPanelMsg.Visibility = DevExpress.XtraBars.Docking.DockVisibility.AutoHide;
            }
        }


        private bool GetIsAlarm()
        {
            // 读取配置文件
            string fileName = Cs01FileHelper.GetCurrDllPath() + @"\Config\connection.xml";
            DataSet ds = new DataSet();
            ds.ReadXml(fileName);

            // 获取连接模式
            if (ds.Tables.Contains("ALARM") == false) return true;

            DataTable table = ds.Tables["ALARM"];
            if (table.Rows.Count == 0) return true;

            DataRow row = table.Rows[0];
            return row["enabled"].ToString().ToUpper().Equals("TRUE");
        }
        #endregion


        private void barButtonItem11_ItemClick(object sender, ItemClickEventArgs e)
        {
            #region 指向简卡模式 王代迪2017-02-16
            DataSet cardDesingn = new SrvBedSideCard().GetCardDesign("ADMIN", "Simple_Card");// Application.StartupPath + "\\BedsideCard\\Simple_Card.xml";
            string fileContent = string.Empty;
            if (cardDesingn != null && cardDesingn.Tables[0].Rows.Count > 0)
                fileContent = cardDesingn.Tables[0].Rows[0]["COLUMNXML"].ToString();
            string fileName = Application.StartupPath+ "\\BedSideCard.xml"; // 指定文件路径及名称
            File.WriteAllText(fileName, fileContent);
            //if (GetModuleHandle("devenv.exe").ToInt32() == 0)
            //if (System.Diagnostics.Debugger.IsAttached)
            //{
            //    fileName = Application.StartupPath.Replace(@"bin\Debug", "") + "\\BedsideCard\\Simple_Card.xml";
            //}
            colClinicPath.Visible = false;
            layoutViewColumn3.Visible = false;
            layoutViewColumn4.Visible = false;
            layoutViewColumn3.Visible = false;
            layoutViewColumn5.Visible = false;
            layoutViewColumn6.Visible = false;
            layoutViewColumn7.Visible = false;
            layoutViewColumn8.Visible = false;
            layoutViewColumn9.Visible = false;
            layoutView.RestoreLayoutFromXml(fileName); //加载布局
            File.Delete(fileName);
            layoutView.CardMinSize = new Size(203, 85);
            #endregion
        }

        /// <summary>
        /// 进入繁卡模式 王代迪2017-02-16
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barButtonItem12_ItemClick(object sender, ItemClickEventArgs e)
        {
            Form frm1 = PlatCommon.Common.PublicFunction.GetFormInDll("Tjhis.Nurinp.Station.dll", "Tjhis.Nurinp.Station.NurRec.frmToDoList");
            frm1.ShowDialog();

            //string fileName = Application.StartupPath + "\\BedsideCard\\Complex_Card.xml";
            ////if (GetModuleHandle("devenv.exe").ToInt32() == 0)
            //if (System.Diagnostics.Debugger.IsAttached)
            //{
            //    fileName = Application.StartupPath.Replace(@"bin\Debug", "") + "\\BedsideCard\\Complex_Card.xml";
            //}
            //colClinicPath.Visible = false;
            //layoutViewColumn3.Visible = false;
            //layoutViewColumn4.Visible = false;
            //layoutViewColumn3.Visible = false;
            //layoutViewColumn5.Visible = false;
            //layoutViewColumn6.Visible = false;
            //layoutViewColumn7.Visible = false;
            //layoutViewColumn8.Visible = false;
            //layoutViewColumn9.Visible = false;
            //layoutView.RestoreLayoutFromXml(fileName); //加载布局
            //layoutView.CardMinSize = new Size(180, 168);
        }

        /// <summary>
        /// 自定义卡模式 王代迪2017-02-16
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barButtonItem13_ItemClick(object sender, ItemClickEventArgs e) 
        {
            try
            {
                DataSet cardDesingn = new SrvBedSideCard().GetCardDesign(PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO, "BedsideCard-" + SystemParm.LoginUser.EMP_NO); //Application.StartupPath + "\\BedsideCard\\BedsideCard-" + PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME + ".xml";
                string fileContent = string.Empty;
                if (cardDesingn != null && cardDesingn.Tables[0].Rows.Count > 0)
                    fileContent = cardDesingn.Tables[0].Rows[0]["COLUMNXML"].ToString();
                string fileName = Application.StartupPath+ "\\BedSideCard.xml"; // 指定文件路径及名称
                File.WriteAllText(fileName, fileContent);
                if (System.IO.File.Exists(fileName) == false)
                {
                    MessageHelper.ShowError("没有找到自定义卡文件，请先设置！");
                    return;
                }
                layoutViewColumn3.Visible = false;
                layoutViewColumn4.Visible = false;
                layoutViewColumn3.Visible = false;
                layoutViewColumn5.Visible = false;
                layoutViewColumn6.Visible = false;
                layoutViewColumn7.Visible = false;
                layoutViewColumn8.Visible = false;
                layoutViewColumn9.Visible = false;
                layoutView.RestoreLayoutFromXml(fileName); //加载布局
                File.Delete(fileName);
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }

        /// <summary>
        /// 患者类型下拉选择  randb 20170901 添加
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void repositoryItemLookUpEdit1_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            barEditItem2.EditValue = e.NewValue.ToString();

            switch (e.NewValue.ToString())
            {
                case "所有患者":
                    myDs.Tables[0].DefaultView.RowFilter = "";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "在院患者":
                    myDs.Tables[0].DefaultView.RowFilter = (e.NewValue.ToString().Equals("所有患者") ? "" : "PATIENT_ID IS NOT NULL");
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "新入患者":
                    DateTime dt = new NM_Service.NMService.ServerPublicClient().GetSysDate();

                    myDs.Tables[0].DefaultView.RowFilter = "ADM_WARD_DATE_TIME >= '" + dt.ToShortDateString() + " 00:00:00' AND "
                        + "ADM_WARD_DATE_TIME <= '" + dt.ToShortDateString() + " 23:59:59'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Lavender;
                    break;
                case "男性患者":
                    myDs.Tables[0].DefaultView.RowFilter = "SEX = '男'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "女性患者":
                    myDs.Tables[0].DefaultView.RowFilter = "SEX = '女'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "特级护理":
                    myDs.Tables[0].DefaultView.RowFilter = "NURSING_CLASS = '0'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "一级护理":
                    myDs.Tables[0].DefaultView.RowFilter = "NURSING_CLASS = '1'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "二级护理":
                    // XtraMessageBox.Show("tishi", "二级护理！");
                    myDs.Tables[0].DefaultView.RowFilter = "NURSING_CLASS = '2'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "三级护理":
                    // XtraMessageBox.Show("tishi", "三级级护理！");
                    myDs.Tables[0].DefaultView.RowFilter = "NURSING_CLASS = '3'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "新生儿":
                    myDs.Tables[0].DefaultView.RowFilter = "IS_NEWBORN = '1'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "跌倒 / 坠床评分高风险患者":
                    SrvBedSideCard client = new SrvBedSideCard();
                    myDs = 
                        client.GetBedSideCardHighRiskPatient(this.WardCode, SystemParm.HisUnitCode);
                    myDs.Tables[0].DefaultView.RowFilter = "HIGH_RISK = '1'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                    
                case "精确查找":
                    TextInputFrm frm = new TextInputFrm();
                    frm.Text = "请输入床号 或 床标号 或 患者ID号 或 患者姓名 ";
                    if (frm.ShowDialog() != DialogResult.OK) return;
                    FocusBed_ByBED_NO_OR_NAME(frm.InputedText);
                    break;
            }
        }
        /// <summary>
        /// 自定义卡片字段
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void layoutView_CustomDrawCardFieldValue(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            // Added by Wangdaidi 9:18 2021 / 11 / 24 D#431 调整护理病历床头卡床标号和病人姓名的显示方式
            LayoutView view = sender as LayoutView;
            DataRow row = layoutView.GetDataRow(e.RowHandle);
            if (e.CellValue != null && !string.IsNullOrEmpty(e.CellValue.ToString()))
            {
                switch (e.Column.FieldName)
                {
                    case "PATIENT_STATUS_NAME":
                        if (e.CellValue.ToString().Contains("危") || e.CellValue.ToString().Contains("重"))
                        {
                            e.Appearance.ForeColor = Color.Red;
                        }
                        break;

                    case "ADM_WARD_DATE_TIME":
                        if (((DateTime)(e.CellValue)).Date.Equals(dtNow.Date))
                        {
                            e.Appearance.ForeColor = Color.Red;
                        }
                        break;

                    case "PREPAYMENT_BAL"://余额
                        if (decimal.Parse(e.CellValue.ToString()) < 0)
                        {
                            e.Appearance.ForeColor = System.Drawing.Color.Red;
                        }
                        break;
                    // Added by Wangdaidi 9:18 2021/11/24 D#431 调整护理病历床头卡床标号和病人姓名的显示方式
                    case "NAME":                //患者姓名
                        SetClassColor(row, e);  // 根据护理等级设置背景颜
                        break;
                    case "BED_LABEL":           //床标号
                        SetClassColor(row, e);  // 根据护理等级设置背景颜
                        break;
                    case "CLINICAL_PATHWAY_STATUS":
                        e.Appearance.ForeColor = Color.Red;
                        break;

                    case "DECANNULATIONFLAG":
                        e.Appearance.ForeColor = Color.Red;
                        break;
                }
            }
        }

        /// <summary>
        /// 根据护理等级设置背景颜色 Added by Wangdaidi 9:18 2021/11/24 D#431 调整护理病历床头卡床标号和病人姓名的显示方式
        /// </summary>
        /// <param name="row"></param>
        /// <param name="e"></param>
        private void SetClassColor(DataRow row, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            // 确认护理等级不为空，则根据护理等级设置对应字段的背景颜色
            if (row["NURSING_CLASS_NAME"] != DBNull.Value)
            {
                switch (row["NURSING_CLASS_NAME"].ToString())
                {
                    case "特级护理":
                        e.Appearance.BackColor = ColorTranslator.FromHtml(getcolorcode(0));
                        break;
                    case "一级护理":
                        e.Appearance.BackColor = ColorTranslator.FromHtml(getcolorcode(1));
                        break;
                    case "二级护理":
                        e.Appearance.BackColor = ColorTranslator.FromHtml(getcolorcode(2));
                        break;
                    case "三级护理":
                        e.Appearance.BackColor = ColorTranslator.FromHtml(getcolorcode(3));
                        break;
                }
            }
        }
        bool locked;
        bool forward;
        int visibleIndex;
        int itemsInRow;
        private void layoutView_VisibleRecordIndexChanged(object sender, LayoutViewVisibleRecordIndexChangedEventArgs e)
        {
            LayoutView layoutView = sender as LayoutView;
            LayoutViewInfo viewInfo = layoutView.GetViewInfo() as LayoutViewInfo;
            int visibleCards = viewInfo.VisibleCards.Count;
            if (visibleCards == 0) return;
            int firstRow = viewInfo.VisibleCards[0].VisibleRow;
            int lastRow = viewInfo.VisibleCards[viewInfo.VisibleCards.Count - 1].VisibleRow;
            int rowCount = lastRow - firstRow + 1;
            if (e.PrevVisibleRecordIndex == 0)
            {
                itemsInRow = visibleCards / rowCount;
            }

            if (locked) return;
            locked = true;
            forward = visibleIndex < (sender as LayoutView).VisibleRecordIndex ? true : false;
            layoutView.VisibleRecordIndex = e.PrevVisibleRecordIndex;
            if (forward)
            {
                layoutView.VisibleRecordIndex += itemsInRow;
            }
            else
            {
                layoutView.VisibleRecordIndex -= itemsInRow;
            }

            locked = false;
            visibleIndex = layoutView.VisibleRecordIndex;
        }


        //获取护理级别颜色
        void GetNursColor()
        {
            //获取护理级别显示的颜色
            dscolor = new SrvBedSideCard().colorcode();
            for (int i = 0; i < dscolor.Tables[0].Rows.Count; i++)
            {
                int code = dscolor.Tables[0].Rows[i]["NURSING_CLASS_CODE"].ToInt();
                string colorcode = dscolor.Tables[0].Rows[i]["NURSING_SHOW_COLOR"].ToString();
                switch (code)
                {
                    case 0: //特级
                        barStaticItem3.ImageOptions.Image = Conver(colorcode);
                        break;
                    case 1: //一级
                        barStaticItem4.ImageOptions.Image = Conver(colorcode);
                        break;
                    case 2: //二级
                        barStaticItem5.ImageOptions.Image = Conver(colorcode);
                        break;
                    case 3: //三级
                        barStaticItem6.ImageOptions.Image = Conver(colorcode);
                        break;
                    case 4: //病重
                        break;
                    case 5: //病危
                        break;
                    case 6: //整体
                        break;
                    default:

                        break;
                }
            }

        }

        //创建颜色图片
        Bitmap Conver(string colorcode)
        {
            Bitmap bitmap = new Bitmap(16, 16, PixelFormat.Format32bppArgb);
            Graphics graphics = Graphics.FromImage(bitmap);
            Color c = ColorTranslator.FromHtml(colorcode);
            graphics.Clear(c);
            graphics.Dispose();
            return bitmap;
        }

        string getcolorcode(int code)
        {
            string where = string.Format("nursing_class_code = '{0}'", code);
            string colorcode = "";
            DataRow[] drs = dscolor.Tables[0].Select(where);
            if (drs.Length > 0)
            {
                colorcode = drs[0]["NURSING_SHOW_COLOR"].ToString();
            }
            return colorcode;
        }

        private void layoutView_CustomDrawCardCaption(object sender, LayoutViewCustomDrawCardCaptionEventArgs e)
        {

            LayoutView view = sender as LayoutView;
            var rect = e.CaptionBounds;
            Color _Mycolor2 = Color.FromArgb(0, 0, 0, 0);
            SolidBrush backBrush = new SolidBrush(_Mycolor2);
            object obj = layoutView.GetRow(e.RowHandle);
            if (obj == null) return;
            DataRow row = (obj as DataRowView).Row;

            if (row["NURSING_CLASS_NAME"] != DBNull.Value)
            {
                switch (row["NURSING_CLASS_NAME"].ToString())
                {
                    case "特级护理":
                        backBrush = new SolidBrush(ColorTranslator.FromHtml(getcolorcode(0)));
                        break;
                    case "一级护理":
                        backBrush = new SolidBrush(ColorTranslator.FromHtml(getcolorcode(1)));
                        break;
                    case "二级护理":
                        backBrush = new SolidBrush(ColorTranslator.FromHtml(getcolorcode(2)));
                        break;
                    case "三级护理":
                        backBrush = new SolidBrush(ColorTranslator.FromHtml(getcolorcode(3)));
                        break;
                    case "普通护理":
                        //backBrush = new SolidBrush(Color.Transparent);
                        break;
                    default:
                        //backBrush = new SolidBrush(Color.Transparent);
                        break;
                }
                e.Graphics.FillRectangle(backBrush, rect);
                e.Appearance.DrawString(e.Cache, view.GetCardCaption(e.RowHandle), e.CaptionBounds);
                e.Handled = true;
            }
        }

        private void timerScroll_Tick(object sender, EventArgs e)
        {
            try
            {
                if (layoutView.IsLastRow)
                {
                    layoutView.MoveFirst();
                }
                else
                {
                    layoutView.MoveNextPage();
                }

                //int focusedRow = this.layoutView.FocusedRowHandle;
                //focusedRow++;
                //if (focusedRow >= this.layoutView.DataRowCount)
                //{
                //    this.LoadDataSet();
                //    gridControl1.DataSource = myDs.Tables[0];

                //    focusedRow = 0;
                //}

                //this.layoutView.FocusedRowHandle = focusedRow;
                //this.layoutView.SelectRow(this.layoutView.FocusedRowHandle);
            }
            catch (Exception ex)
            {
                //MessageHelper.ShowError(ex.Message);
            }
        }
        DevExpress.XtraGrid.Views.Layout.ViewInfo.LayoutViewHitInfo m_DownHitInfo_TuXing = null;
        private bool m_TmrDragDropCanRun = false;
        public class DragDropData
        {
            public DevExpress.XtraGrid.Views.Layout.LayoutView Sender { get; set; }

            public object Data { get; set; }
        }
        private void layoutView_MouseDown(object sender, MouseEventArgs e)
        {
            if (this.AppCode == "NURINP" || this.AppCode == "NURSWS")
            {
                DevExpress.XtraGrid.Views.Layout.ViewInfo.LayoutViewHitInfo hi = layoutView.CalcHitInfo(new Point(e.X, e.Y));
                int iMouseRowHandle = hi.RowHandle;
                if (iMouseRowHandle >= 0 && e.Button == MouseButtons.Left)
                {
                    m_DownHitInfo_TuXing = hi;
                }
            }
        }

        private void layoutView_MouseMove(object sender, MouseEventArgs e)
        {
            if (this.AppCode == "NURINP" || this.AppCode == "NURSWS")
            {
                DevExpress.XtraGrid.Views.Layout.LayoutView view = sender as DevExpress.XtraGrid.Views.Layout.LayoutView;
                if (e.Button == MouseButtons.Left && m_DownHitInfo_TuXing != null)
                {
                    Size dragSize = SystemInformation.DragSize;
                    Rectangle dragRect = new Rectangle(new Point(m_DownHitInfo_TuXing.HitPoint.X - dragSize.Width / 2, m_DownHitInfo_TuXing.HitPoint.Y - dragSize.Height / 2), dragSize);

                    //当鼠标离开原来的控件区域之后才显示拖拽效果  
                    if (!dragRect.Contains(new Point(e.X, e.Y)))
                    {
                        m_TmrDragDropCanRun = true;
                        DataRow row = view.GetDataRow(m_DownHitInfo_TuXing.RowHandle);
                        DragDropData modelData = new DragDropData();
                        modelData.Sender = layoutView;
                        modelData.Data = row;
                        view.GridControl.DoDragDrop(modelData, DragDropEffects.Move);
                        m_DownHitInfo_TuXing = null;
                        DevExpress.Utils.DXMouseEventArgs.GetMouseArgs(e).Handled = true;
                    }
                }
            }
        }

        private void gridControl1_DragDrop(object sender, DragEventArgs e)
        {
            try
            {
                if (this.AppCode == "NURINP" || this.AppCode == "NURSWS")
                {
                    m_TmrDragDropCanRun = false;
                    Point pt = gridControl1.PointToClient(new Point(e.X, e.Y));
                    DevExpress.XtraGrid.Views.Layout.ViewInfo.LayoutViewHitInfo hi = layoutView.CalcHitInfo(pt);
                    DragDropData modelData = (DragDropData)e.Data.GetData(typeof(DragDropData));
                    switch (modelData.Sender.Name)
                    {
                        case "layoutView":
                            DataRow mROW = modelData.Sender.GetDataRow(m_DownHitInfo_TuXing.RowHandle);
                            DataRow mROW2 = modelData.Sender.GetDataRow(hi.RowHandle);
                            if (mROW != null && mROW2 != null)
                            {
                                BedExchage(mROW, mROW2);
                                RefreshData();
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowError(ex.Message);
            }
            finally
            {

            }
        }

        private void gridControl1_DragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(typeof(DragDropData)))
                e.Effect = DragDropEffects.Move;
            else
                e.Effect = DragDropEffects.None;
        }

        private void BedExchage(DataRow mROW, DataRow mROW2)
        {
            try
            {
                ArrayList arrData = new ArrayList();

                // 保存 病人床位使用记录
                string bedNo1 = "" + mROW["BED_NO"];
                string bedNo2 = "" + mROW2["BED_NO"];
                if (bedNo1 == bedNo2)
                    return;
                // 母亲换床，婴儿床处理
                string PATIENT_ID1 = "" + mROW["PATIENT_ID"].ToString().Trim();
                string VISIT_ID1 = "" + mROW["VISIT_ID"].ToString().Trim();
                string PATIENT_ID2 = "" + mROW2["PATIENT_ID"].ToString().Trim();
                string VISIT_ID2 = "" + mROW2["VISIT_ID"].ToString().Trim();
                SrvBedExchange srvLocal = new SrvBedExchange();
                // 母亲结束旧床位的使用 更新pats_bed_record 
                Dictionary<string, string> idc = new Dictionary<string, string>();
                string sql = srvLocal.GetSql_PatsBedRec_End(this.WardCode, PATIENT_ID1, VISIT_ID1);
                idc.Add(sql, "修改pats_bed_record出错");

                // 母亲开始新床位的使用
                sql = srvLocal.GetSql_PatsBedRec_Begin(this.WardCode, bedNo2, PATIENT_ID1, VISIT_ID1);
                idc.Add(sql, "插入pats_bed_record出错");

                // 取新生儿id  ,床位信息
                DataTable dt = srvLocal.GetBayPatientIDByMother(PATIENT_ID1).Tables[0];
                foreach (DataRow dr in dt.Rows)
                {
                    string patientID_Baby = dr["patient_id"].ToString();
                    if (string.IsNullOrEmpty(patientID_Baby)) continue;

                    DataTable dtPats_In_Hospital = srvLocal.GetBedNoByPatientID(patientID_Baby).Tables[0];
                    if (Cs02DataSetHelper.HasRecord(dtPats_In_Hospital) == false) continue;

                    int bedNo_Baby = Converter.ToInt(dtPats_In_Hospital.Rows[0]["bed_no"]);

                    // 婴儿结束旧床位                        
                    sql = srvLocal.GetSql_PatsBedRec_End(this.WardCode, patientID_Baby, "1");
                    idc.Add(sql, "修改pats_bed_record出错");

                    // 婴儿开始新床位
                    sql = srvLocal.GetSql_PatsBedRec_Begin(this.WardCode, bedNo_Baby.ToString(), patientID_Baby, "1");
                    idc.Add(sql, "插入pats_bed_record出错");
                }

                if (string.IsNullOrEmpty(PATIENT_ID2) == false)
                {
                    sql = srvLocal.GetSql_PatsBedRec_End(this.WardCode, PATIENT_ID2, VISIT_ID2);
                    idc.Add(sql, "修改pats_bed_record出错");

                    // 母亲开始新床位的使用
                    sql = srvLocal.GetSql_PatsBedRec_Begin(this.WardCode, bedNo2, PATIENT_ID2, VISIT_ID2);
                    idc.Add(sql, "插入pats_bed_record出错");

                    // 取新生儿id  ,床位信息
                    dt = srvLocal.GetBayPatientIDByMother(PATIENT_ID2).Tables[0];
                    foreach (DataRow dr in dt.Rows)
                    {
                        string patientID_Baby = dr["patient_id"].ToString();
                        if (string.IsNullOrEmpty(patientID_Baby)) continue;

                        DataTable dtPats_In_Hospital = srvLocal.GetBedNoByPatientID(patientID_Baby).Tables[0];
                        if (Cs02DataSetHelper.HasRecord(dtPats_In_Hospital) == false) continue;

                        int bedNo_Baby = Converter.ToInt(dtPats_In_Hospital.Rows[0]["bed_no"]);

                        // 婴儿结束旧床位                        
                        sql = srvLocal.GetSql_PatsBedRec_End(this.WardCode, patientID_Baby, "1");
                        idc.Add(sql, "修改pats_bed_record出错");

                        // 婴儿开始新床位
                        sql = srvLocal.GetSql_PatsBedRec_Begin(this.WardCode, bedNo_Baby.ToString(), patientID_Baby, "1");
                        idc.Add(sql, "插入pats_bed_record出错");
                    }
                }
                arrData.Add(idc);

                // 进行换床
                string bedStatusFrom = mROW["BED_STATUS"] == DBNull.Value ? "" : mROW["BED_STATUS"].ToString();
                string bedStatusTo = mROW2["BED_STATUS"] == DBNull.Value ? "" : mROW2["BED_STATUS"].ToString();
                string bedLabelFrom = mROW2["BED_STATUS"] == DBNull.Value ? "" : mROW["BED_LABEL"].ToString();
                string bedLabelTo = mROW2["BED_STATUS"] == DBNull.Value ? "" : mROW2["BED_LABEL"].ToString();
                string patient_IDFrom = mROW2["BED_STATUS"] == DBNull.Value ? "" : mROW["PATIENT_ID"].ToString();
                string patient_IDTo = mROW2["BED_STATUS"] == DBNull.Value ? "" : mROW2["PATIENT_ID"].ToString();
                int ret = srvLocal.ExchangeBed(bedNo1, bedStatusFrom, bedLabelFrom, patient_IDFrom, bedNo2, bedStatusTo, bedLabelTo, patient_IDTo, this.WardCode, ref arrData);

                ArrayList list = new ArrayList();
                Dictionary<string, string> item = null;
                ArrayList item2 = null;
                string strTemp = "";
                for (int i = 0; i < arrData.Count; i++)
                {
                    item = null;
                    item2 = null;
                    item = arrData[i] as Dictionary<string, string>;
                    if (item == null)
                    {
                        item2 = arrData[i] as ArrayList;
                    }
                    if (item != null && item.Count > 0)
                    {

                        foreach (var di in item)
                        {
                            strTemp = Convert.ToString(di.Key);

                            list.Add(strTemp);

                        }
                    }
                    if (item2 != null && item2.Count > 0)
                    {
                        for (int k = 0; k < item2.Count; k++)
                        {
                            strTemp = Convert.ToString(item2[k]);

                            list.Add(strTemp);

                        }

                    }
                }
                new DBSrv().ExecuteWithTrans(list);
            }
            catch (Exception ex)
            {
                MessageHelper.ShowInformation(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, "", this.GetType().Name);
            }
        }


        /// <summary>
        /// 自定义字段显示， Added by Wangdaidi 9:18 2021/11/24 D#431 调整护理病历床头卡床标号和病人姓名的显示方式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void layoutView_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            // 若为床标号字段，则将字段内容里添加"床"字
            if(e.Column.FieldName == "BED_LABEL")
            {
                e.DisplayText = e.Value.ToString() + "床";
            }
            // 防止自动计算卡大小
            if (layoutView.CardMinSize.Height > 0)
            {
                layoutView.CardMinSize = new Size(0, 0);
            }
        }
        /// <summary>
        /// 鼠标双击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void layoutView_DoubleClick(object sender, EventArgs e)
        {
            DevExpress.XtraGrid.Views.Layout.ViewInfo.LayoutViewHitInfo hi = layoutView.CalcHitInfo(new Point(((System.Windows.Forms.MouseEventArgs)e).X, ((System.Windows.Forms.MouseEventArgs)e).Y));
            object obj = layoutView.GetRow(hi.RowHandle);
            if (obj == null) return;
            DataRow row = (obj as DataRowView).Row;
            if (!string.IsNullOrEmpty(row["PATIENT_ID"].ToString()))
            {
                Clipboard.SetText(row["PATIENT_ID"].ToString());
            }
        }
        /// <summary>
        /// 双击消息提醒自动跳转
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridViewMsg_DoubleClick(object sender, EventArgs e)
        {
            DataRow drFocus = gridViewMsg.GetFocusedDataRow();
            if (drFocus == null) return;
            List<string> lstCols = new List<string>();
            lstCols.Add("ORDERSSUBMIT");
            lstCols.Add("ORDERSVERIFY");
            string strFocusCell = drFocus[lstCols[0]].ToString();
            if (gridViewMsg.FocusedColumn != null && lstCols.Contains(gridViewMsg.FocusedColumn.FieldName) && !string.IsNullOrEmpty(drFocus[gridViewMsg.FocusedColumn.FieldName].ToString()))
            {
                strFocusCell = drFocus[gridViewMsg.FocusedColumn.FieldName].ToString();
            }
            else
            {
                foreach (string strCol in lstCols)
                {
                    if (!string.IsNullOrEmpty(drFocus[strCol].ToString()))
                    {
                        strFocusCell = drFocus[strCol].ToString();
                    }
                }
            }
            if (strFocusCell == "新医嘱")
            {
                Tjhis.Nurinp.Station.Orders.frmOrdersBook frm1 = new Orders.frmOrdersBook();
                if (frm1 != null)
                {
                    //frm1.PatientInfo = drFocus;
                    //// 设置参数
                    //frm1.StartPosition = FormStartPosition.CenterScreen;
                    ////窗体变量赋值
                    //frm1.DeptCode = this.DeptCode;
                    //frm1.WardCode = this.WardCode;
                    //frm1.AppCode = this.AppCode;
                    //frm1.DeptList = this.DeptList;
                    //frm1.WardList = this.WardList;
                    //frm1.WardName = this.WardName;
                    //frm1.DeptName = this.DeptName;
                    //frm1.Clinical_Pathway = this.Clinical_Pathway;
                    SetFormParamValue(frm1, drFocus);
                    frm1.Tag = this.MdiParent;
                    //PlatCommon.SysBase.SystemParm.openNurswsFormHandle(frm1, string.Empty);

                    Control parentControl = this.Parent.Parent;
                    if (parentControl != null && parentControl.GetType() == typeof(FrmNewMain))
                    {
                        FrmNewMain main = (FrmNewMain)parentControl;
                        main.openForm(frm1, string.Empty);
                    }
                }
            }
            else if (strFocusCell == "待审核")
            {
                Tjhis.Nurinp.Station.Orders.frmOrderAudit frm1 = new Orders.frmOrderAudit();
                if (frm1 != null)
                {
                    //frm1.PatientInfo = drFocus;
                    //// 设置参数
                    //frm1.StartPosition = FormStartPosition.CenterScreen;
                    ////窗体变量赋值
                    //frm1.DeptCode = this.DeptCode;
                    //frm1.WardCode = this.WardCode;
                    //frm1.AppCode = this.AppCode;
                    //frm1.DeptList = this.DeptList;
                    //frm1.WardList = this.WardList;
                    //frm1.WardName = this.WardName;
                    //frm1.DeptName = this.DeptName;
                    //frm1.Clinical_Pathway = this.Clinical_Pathway;
                    SetFormParamValue(frm1, drFocus);

                    frm1.Tag = this.MdiParent;
                    //PlatCommon.SysBase.SystemParm.openNurswsFormHandle(frm1, string.Empty);
                    Control parentControl = this.Parent.Parent;
                    if (parentControl != null && parentControl.GetType() == typeof(FrmNewMain))
                    {
                        FrmNewMain main = (FrmNewMain)parentControl;
                        main.openForm(frm1, string.Empty);
                    }
                }
            }
        }
        /// <summary>
        /// 列表转换
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barButtonItemList_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                if (gridControl1.MainView.GetType().Name == "LayoutView")
                {
                    gridControl1.MainView = gridViewBak;
                    for (int i = 0; i < gridViewBak.Columns.Count; i++)
                    {
                        
                        if (layoutView.Columns.ColumnByFieldName(gridViewBak.Columns[i].FieldName) == null)
                        {
                            gridViewBak.Columns[i].Visible = false;
                        }
                        else 
                        {
                            gridViewBak.Columns[i].Caption = layoutView.Columns[gridViewBak.Columns[i].FieldName].Caption;
                            gridViewBak.Columns[i].Visible = layoutView.Columns[gridViewBak.Columns[i].FieldName].Visible;
                        }
                    }
                    gridViewBak.BestFitColumns();
                }
                else if (gridControl1.MainView.GetType().Name == "GridView")
                {
                    gridControl1.MainView = layoutViewBak;
                }
            }
            catch (Exception ex)
            {

            }
        }
        /// <summary>
        /// 列表点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gridViewBak_RowClick(object sender, RowClickEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataRow drv = gridViewBak.GetDataRow(e.RowHandle);
                if (drv == null) return;
                string pat_id = drv["PATIENT_ID"] == null ? "" : gridViewBak.GetFocusedDataRow()["PATIENT_ID"].ToString();
                if ("".Equals(pat_id))
                {
                    if (drv["NAME"].ToString().Contains("的包床"))
                    {
                        // 隐藏右键菜单
                        ppMenu.HidePopup();
                        ParentForm frm1 = PlatCommon.Common.PublicFunction.GetFormInDll("Tjhis.Nurinp.Station.dll", "Tjhis.Nurinp.Station.ADT.frmContractBed");
                        ((ParentForm)frm1).PatientInfo = gridViewBak.GetFocusedDataRow();
                        frm1.WardCode = WardCode;
                        frm1.AppCode = AppCode;
                        frm1.Show();
                    }
                    else
                    {
                        //return;
                        for (int i = 0; i < ppMenu.ItemLinks.Count; i++)
                        {
                            if (ppMenu.ItemLinks[i].Caption == "患者入科")
                            {
                                ppMenu.ItemLinks[i].Visible = true;
                            }
                            else
                            {
                                ppMenu.ItemLinks[i].Visible = false;
                            }
                        }
                    }
                }
                else
                {
                    for (int i = 0; i < ppMenu.ItemLinks.Count; i++)
                    {
                        if (ppMenu.ItemLinks[i].Caption == "患者入科")
                        {
                            ppMenu.ItemLinks[i].Visible = false;
                        }
                        else
                        {
                            ppMenu.ItemLinks[i].Visible = true;
                        }
                    }
                }
                if (gridViewBak.GetFocusedDataRow()["SEX"].ToString() == "男" || "".Equals(pat_id))
                {
                    for (int i = 0; i < ppMenu.ItemLinks.Count; i++)
                    {
                        if (ppMenu.ItemLinks[i].Caption == "产程图")
                        {
                            ppMenu.ItemLinks[i].Visible = false;
                            break;
                        }
                    }

                }
                else
                {
                    for (int i = 0; i < ppMenu.ItemLinks.Count; i++)
                    {
                        if (ppMenu.ItemLinks[i].Caption == "产程图")
                        {
                            ppMenu.ItemLinks[i].Visible = true;
                            break;
                        }
                    }
                }
                ppMenu.ShowPopup(PointToScreen(e.Location));
            }
            else if (e.Clicks >= 2)
            {
                int ret = orderList.IndexOf(e.RowHandle);
                if (ret < 0) return;
                object obj = gridViewBak.GetRowCellValue(e.RowHandle, bc);
                string baochuang = obj == null ? "" : obj.ToString();
                if (!string.IsNullOrEmpty(baochuang))
                {
                    return;
                }
                myDs.Tables[0].Columns["ORDERSSTATUS"].ReadOnly = false;
                gridViewBak.SetRowCellValue(e.RowHandle, colORDERSSTATUS, myImagenull);
                orderList.Remove(e.RowHandle);
                //打开医嘱窗口
                ParentForm frm1 = PlatCommon.Common.PublicFunction.GetFormInDll("NursingPlatform.exe", "NurInp.View.Orders.OrderAuditFrm");
                if (frm1 != null)
                {
                    frm1.StartPosition = FormStartPosition.CenterScreen;
                    frm1.PatientInfo = gridViewBak.GetDataRow(e.RowHandle);
                    frm1.ShowDialog();
                }
            }
        }

        private void frmBedSideCard_FormClosed(object sender, FormClosedEventArgs e)
        {
            refreshCardEvent -= refreshCard;
            GC.Collect();

        }

        private void frmBedSideCard_FormClosed_1(object sender, FormClosedEventArgs e)
        {
            timerOrderAlert.Enabled = false;
            timer1.Enabled = false;
            timerMsg.Enabled = false;
            timerScroll.Enabled = false;
            refreshCardEvent -= refreshCard;
            if (player != null)
            {
                player.Stop(); isLoopPlayed = false;
            }
            GC.Collect();
        }

        private void repositoryItemRadioGroup1_EditValueChanged(object sender, EventArgs e)
        {
            RadioGroup group = sender as RadioGroup;
            string value = group.Properties.Items[group.SelectedIndex].Value.ToString();
            string fileContent = string.Empty;// Application.StartupPath + "\\BedsideCard\\Default_Card.xml"; // 床头卡样式文件
            if (value == "0")
            {
                DataSet cardDesingn = new SrvBedSideCard().GetCardDesign("ADMIN", "Default_Card"); // 床头卡样式文件
                if (cardDesingn != null && cardDesingn.Tables[0].Rows.Count > 0)
                    fileContent = cardDesingn.Tables[0].Rows[0]["COLUMNXML"].ToString();
                if (PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO == "ADMIN")
                    this.barBtnCustomization.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
                else
                    this.barBtnCustomization.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            else if(value=="1")//
            {
                DataSet cardDesingn = new SrvBedSideCard().GetCardDesign("ADMIN", "Simple_Card"); // 床头卡样式文件
                if (cardDesingn != null && cardDesingn.Tables[0].Rows.Count > 0)
                    fileContent = cardDesingn.Tables[0].Rows[0]["COLUMNXML"].ToString();
                if (PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO == "ADMIN")
                    this.barBtnCustomization.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
                else
                    this.barBtnCustomization.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            else
            {
              DataSet  cardDesingn = new SrvBedSideCard().GetCardDesign(PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO, "BedsideCard_" + SystemParm.LoginUser.EMP_NO); // 床头卡样式文件
                if (PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO == "ADMIN")
                    this.barBtnCustomization.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
                else
                    this.barBtnCustomization.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
                if (cardDesingn != null && cardDesingn.Tables[0].Rows.Count > 0)
                    fileContent = cardDesingn.Tables[0].Rows[0]["COLUMNXML"].ToString();
                else
                    return;
            }
            string fileName = Application.StartupPath+ "\\BedSideCard.xml"; // 指定文件路径及名称
            File.WriteAllText(fileName, fileContent);
            layoutView.RestoreLayoutFromXml(fileName); //加载布局
            File.Delete(fileName);
            layoutView.CardMinSize = new Size(0, 0);
            layoutViewBak = layoutView;
            if (gridControl1.MainView.GetType().Name == "GridView")
            {
                gridControl1.MainView = layoutViewBak;
            }
        }

        private void repositoryItemLookUpEdit1_EditValueChanged(object sender, EventArgs e)
        {
           var  key = ((LookUpEdit)sender).EditValue.ToString();

            switch (key)
            {
                case "所有患者":
                    myDs.Tables[0].DefaultView.RowFilter = "";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "在院患者":
                    myDs.Tables[0].DefaultView.RowFilter = (key.Equals("所有患者") ? "" : "PATIENT_ID IS NOT NULL");
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "新入患者":
                    DateTime dt = new NM_Service.NMService.ServerPublicClient().GetSysDate();

                    myDs.Tables[0].DefaultView.RowFilter = "ADM_WARD_DATE_TIME >= '" + dt.ToShortDateString() + " 00:00:00' AND "
                        + "ADM_WARD_DATE_TIME <= '" + dt.ToShortDateString() + " 23:59:59'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Lavender;
                    break;
                case "男性患者":
                    myDs.Tables[0].DefaultView.RowFilter = "SEX = '男'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "女性患者":
                    myDs.Tables[0].DefaultView.RowFilter = "SEX = '女'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "特级护理":
                    myDs.Tables[0].DefaultView.RowFilter = "NURSING_CLASS = '0'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "一级护理":
                    myDs.Tables[0].DefaultView.RowFilter = "NURSING_CLASS = '1'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "二级护理":
                    // XtraMessageBox.Show("tishi", "二级护理！");
                    myDs.Tables[0].DefaultView.RowFilter = "NURSING_CLASS = '2'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "三级护理":
                    // XtraMessageBox.Show("tishi", "三级级护理！");
                    myDs.Tables[0].DefaultView.RowFilter = "NURSING_CLASS = '3'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "新生儿":
                    myDs.Tables[0].DefaultView.RowFilter = "IS_NEWBORN = '1'";
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Lavender;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                case "跌倒/坠床评分高风险患者":
                    SrvBedSideCard client = new SrvBedSideCard();
                    DataSet patientDs =
                        client.GetBedSideCardHighRiskPatient(this.WardCode, SystemParm.HisUnitCode);
                    if (DataSetHelper.HasRecord(patientDs))
                    {
                        List<object> columnValues = patientDs.Tables[0].AsEnumerable()
                                     .Select(row => row["MPATIENT_ID"])
                                     .ToList();
                        string patientIdStr = string.Join("','", columnValues);
                        patientIdStr = string.Concat("('", patientIdStr, "')");
                        myDs.Tables[0].DefaultView.RowFilter = $@"MPATIENT_ID in {patientIdStr}";

                    }
                    else
                    {
                        myDs.Tables[0].DefaultView.RowFilter = $@"PATIENT_ID in ('')";
                    }
                    barStaticItem4.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem5.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem6.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem3.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem13.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem12.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem11.ItemAppearance.Normal.BackColor = Color.Transparent;
                    barStaticItem14.ItemAppearance.Normal.BackColor = Color.Transparent;
                    break;
                    //case "精确查找":
                    //    TextInputFrm frm = new TextInputFrm();
                    //    frm.Text = "请输入床号 或 床标号 或 患者ID号 或 患者姓名 ";
                    //    if (frm.ShowDialog() != DialogResult.OK) return;
                    //    FocusBed_ByBED_NO_OR_NAME(frm.InputedText);
                    //    break;
            }

        }

        private void barButtonItem8_ItemClick(object sender, ItemClickEventArgs e)
        {
            MessageHelper.ShowSuccess("未检测到危急值系统!");
        }

        private void toolTipController1_GetActiveObjectInfo(object sender, ToolTipControllerGetActiveObjectInfoEventArgs e)
        {
            if (e.Info == null && e.SelectedControl == gridControl1)
            {
                DevExpress.XtraGrid.Views.Layout.ViewInfo.LayoutViewHitInfo info = layoutView.CalcHitInfo(e.ControlMousePosition);
                if (info.InFieldValue)
                {
                    if(info.Column.Name == colClinicPath.Name)
                    {
                        string text = layoutView.GetRowCellDisplayText(info.RowHandle, colClinicPath);
                        if (string.IsNullOrEmpty(text)) return;
                        string cellKey = info.RowHandle.ToString() + " - " + info.Column.ToString();
                        e.Info = new ToolTipControlInfo(cellKey, "临床路径患者");
                    }
                    if (info.Column.Name == DECANNULATIONFLAG.Name)
                    {
                        string text = layoutView.GetRowCellDisplayText(info.RowHandle, DECANNULATIONFLAG);
                        if (string.IsNullOrEmpty(text)) return;
                        string cellKey = info.RowHandle.ToString() + " - " + info.Column.ToString();
                        e.Info = new ToolTipControlInfo(cellKey, "非计划性拔管");
                    }
                }
            }
        }

        /// <summary>
        /// 加载顶部菜单
        /// </summary>
        protected void LoadTopMenu()
        {
            try
            {
                // 创建日常工作主菜单
                BarSubItem dailyWorkMenu = new BarSubItem(barManager1, "日常工作");
                dailyWorkMenu.Name = "日常工作";

                // 创建护理记录单菜单项
                BarButtonItem nursingRecordItem = new BarButtonItem(barManager1, "护理记录单");
                nursingRecordItem.Name = "护理记录单";
                nursingRecordItem.ItemClick += NursingRecordItem_ItemClick;

                // 添加图标（如果存在）
                try
                {
                    string iconPath = Application.StartupPath + "\\Images\\Menu\\Small\\00-护理病历.png";
                    if (System.IO.File.Exists(iconPath))
                    {
                        smallIconmenu.AddImage(Image.FromFile(iconPath));
                        nursingRecordItem.ImageIndex = smallIconmenu.Images.Count - 1;
                    }
                }
                catch
                {
                    // 图标加载失败时忽略
                }

                // 将护理记录单添加到日常工作菜单
                dailyWorkMenu.ItemLinks.Add(nursingRecordItem);

                // 将日常工作菜单添加到主菜单栏
                bar1.ItemLinks.Add(dailyWorkMenu);
            }
            catch (Exception ex)
            {
                // 记录错误但不影响主要功能
                System.Diagnostics.Debug.WriteLine($"加载顶部菜单时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 护理记录单菜单项点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void NursingRecordItem_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                // 获取当前选中的患者信息
                DataRow drPatient = null;
                if (gridControl1.MainView.GetType().Name == "LayoutView")
                {
                    drPatient = layoutView.GetFocusedDataRow();
                }
                else if (gridControl1.MainView.GetType().Name == "GridView")
                {
                    drPatient = gridViewBak.GetFocusedDataRow();
                }

                if (drPatient == null)
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("请先选择一个患者！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 检查是否有患者ID
                string patientId = drPatient["PATIENT_ID"] == null ? "" : drPatient["PATIENT_ID"].ToString();
                if (string.IsNullOrEmpty(patientId))
                {
                    DevExpress.XtraEditors.XtraMessageBox.Show("当前床位没有患者信息！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }


            }
        }

        /// <summary>
        /// 添加日常工作菜单
        /// </summary>
        private void AddDailyWorkMenu()
        {
            try
            {
                // 创建日常工作主菜单
                BarSubItem dailyWorkMenu = new BarSubItem(barManager1, "日常工作");
                dailyWorkMenu.Name = "日常工作";

                // 创建护理记录单菜单项
                BarButtonItem nursingRecordItem = new BarButtonItem(barManager1, "护理记录单");
                nursingRecordItem.Name = "护理记录单";
                nursingRecordItem.ItemClick += (sender, e) => {
                    // 直接调用 SimpleMenuUpdater.ExecuteUpdate() 方法
                    Service.SimpleMenuUpdater.ExecuteUpdate();
                };

                // 将护理记录单添加到日常工作菜单
                dailyWorkMenu.ItemLinks.Add(nursingRecordItem);

                // 将日常工作菜单添加到主菜单栏
                bar1.ItemLinks.Add(dailyWorkMenu);
            }
            catch (Exception ex)
            {
                // 记录错误但不影响主要功能
                System.Diagnostics.Debug.WriteLine($"添加日常工作菜单时发生错误: {ex.Message}");
            }
        }
    }

}
