using System;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;

namespace Tjhis.Nurinp.Station.Tools
{
    /// <summary>
    /// 菜单助手类 - 用于添加护理记录单到日常工作菜单
    /// </summary>
    public static class MenuHelper
    {
        /// <summary>
        /// 在指定的菜单栏中添加日常工作菜单和护理记录单子菜单
        /// </summary>
        /// <param name="barManager">菜单管理器</param>
        /// <param name="mainBar">主菜单栏</param>
        public static void AddNursingRecordMenu(BarManager barManager, Bar mainBar)
        {
            try
            {
                // 检查是否已经存在日常工作菜单
                BarSubItem dailyWorkMenu = FindDailyWorkMenu(mainBar);
                
                if (dailyWorkMenu == null)
                {
                    // 创建日常工作主菜单
                    dailyWorkMenu = new BarSubItem(barManager, "日常工作");
                    dailyWorkMenu.Name = "日常工作";
                    mainBar.ItemLinks.Add(dailyWorkMenu);
                }
                
                // 检查是否已经存在护理记录单菜单项
                if (!HasNursingRecordItem(dailyWorkMenu))
                {
                    // 创建护理记录单菜单项
                    BarButtonItem nursingRecordItem = new BarButtonItem(barManager, "护理记录单");
                    nursingRecordItem.Name = "护理记录单";
                    nursingRecordItem.ItemClick += NursingRecordItem_ItemClick;
                    
                    // 添加到日常工作菜单
                    dailyWorkMenu.ItemLinks.Add(nursingRecordItem);
                    
                    XtraMessageBox.Show("护理记录单菜单已成功添加到日常工作菜单中！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    XtraMessageBox.Show("护理记录单菜单已存在，无需重复添加。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"添加菜单时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// 查找日常工作菜单
        /// </summary>
        /// <param name="mainBar">主菜单栏</param>
        /// <returns>日常工作菜单项，如果不存在返回null</returns>
        private static BarSubItem FindDailyWorkMenu(Bar mainBar)
        {
            foreach (BarItemLink itemLink in mainBar.ItemLinks)
            {
                if (itemLink.Item is BarSubItem subItem && subItem.Caption == "日常工作")
                {
                    return subItem;
                }
            }
            return null;
        }
        
        /// <summary>
        /// 检查日常工作菜单中是否已存在护理记录单菜单项
        /// </summary>
        /// <param name="dailyWorkMenu">日常工作菜单</param>
        /// <returns>是否存在</returns>
        private static bool HasNursingRecordItem(BarSubItem dailyWorkMenu)
        {
            foreach (BarItemLink itemLink in dailyWorkMenu.ItemLinks)
            {
                if (itemLink.Item.Caption == "护理记录单")
                {
                    return true;
                }
            }
            return false;
        }
        
        /// <summary>
        /// 护理记录单菜单项点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private static void NursingRecordItem_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                // 调用 SimpleMenuUpdater.ExecuteUpdate() 方法
                Tjhis.Nurinp.Station.Service.SimpleMenuUpdater.ExecuteUpdate();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"执行护理记录单功能时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
